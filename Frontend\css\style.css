/* ===== Global Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

/* ===== Login Page Styles ===== */
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 450px;
    animation: fadeInUp 0.6s ease-out;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== Header Styles ===== */
.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.logo h1 {
    font-size: 1.8rem;
    color: #333;
    font-weight: 700;
}

.subtitle {
    color: #666;
    font-size: 1rem;
    margin-top: 5px;
}

/* ===== Form Styles ===== */
.login-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group label i {
    color: #667eea;
    width: 16px;
}

.form-group input[type="text"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #667eea;
}

/* ===== Checkbox Styles ===== */
.checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* ===== Button Styles ===== */
.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== Loading Spinner ===== */
.loading-spinner {
    text-align: center;
    padding: 20px;
    color: #667eea;
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.loading-spinner p {
    font-size: 0.95rem;
    color: #666;
}

/* ===== Message Styles ===== */
.error-message,
.success-message {
    padding: 12px 16px;
    border-radius: 8px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95rem;
}

.error-message {
    background: #fee;
    color: #c53030;
    border: 1px solid #fed7d7;
}

.success-message {
    background: #f0fff4;
    color: #38a169;
    border: 1px solid #c6f6d5;
}

/* ===== Demo Credentials ===== */
.demo-credentials {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid #e1e5e9;
}

.demo-credentials h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.demo-credentials h3 i {
    color: #667eea;
}

.demo-user {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.demo-user strong {
    color: #333;
    display: block;
    margin-bottom: 8px;
}

.demo-user span {
    display: block;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.demo-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.85rem;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.demo-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* ===== Footer Styles ===== */
.login-footer {
    text-align: center;
    margin-top: 30px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
}

.system-status {
    margin-top: 10px;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 12px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    font-size: 0.8rem;
}

.status-indicator.online i {
    color: #48bb78;
}

.status-indicator.offline i {
    color: #f56565;
}

.status-indicator.checking i {
    color: #ed8936;
    animation: pulse 1.5s infinite;
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* ===== Responsive Design ===== */
@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }
    
    .login-card {
        padding: 30px 20px;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .logo i {
        font-size: 2rem;
    }
}
