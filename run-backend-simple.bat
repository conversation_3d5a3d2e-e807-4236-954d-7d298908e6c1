@echo off
echo ========================================
echo    ElectroHub Pro - Quick Start
echo ========================================
echo.

echo Checking if .NET is installed...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ .NET SDK not found!
    echo.
    echo Please install .NET 8 SDK from:
    echo https://dotnet.microsoft.com/download
    echo.
    echo After installation, restart this script.
    pause
    exit /b 1
)

echo ✅ .NET SDK found
echo.

echo Starting ASP.NET Backend...
echo.
echo Backend will be available at:
echo - https://localhost:7001 (HTTPS)
echo - http://localhost:5001 (HTTP)
echo.
echo API Endpoints:
echo - https://localhost:7001/api/test
echo - https://localhost:7001/swagger
echo.

cd Backend\ElectronicsStore.WebAPI

echo Building project...
dotnet build --configuration Release --verbosity quiet

if %errorlevel% neq 0 (
    echo.
    echo ❌ Build failed! Check the errors above.
    pause
    exit /b 1
)

echo ✅ Build successful
echo.
echo ========================================
echo 🚀 Starting server...
echo Press Ctrl+C to stop
echo ========================================
echo.

dotnet run --configuration Release

pause
