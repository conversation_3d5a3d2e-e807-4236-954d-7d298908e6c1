# 🚀 ElectroHub Pro - دليل التشغيل مع ASP.NET Backend

## 📋 المتطلبات

### 1. تثبيت .NET SDK
- قم بتحميل .NET 8 SDK من: https://dotnet.microsoft.com/download
- تأكد من تثبيت SQL Server أو SQL Server Express

### 2. إعداد قاعدة البيانات
- تأكد من تشغيل SQL Server
- سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل

## 🏃‍♂️ تشغيل النظام

### 1. تشغيل ASP.NET Backend (الخادم)
```bash
cd Backend\ElectronicsStore.WebAPI
dotnet run
```
أو استخدم ملف التشغيل:
```bash
start-backend.bat
```

### 2. فتح Frontend (الواجهة)
افتح ملف `Frontend/dash.html` في المتصفح

## 🎯 الميزات المتاحة

### ✅ إدارة المنتجات (مربوطة بالـ Backend)
- **إضافة منتجات جديدة** مع الصور
- **تعديل المنتجات** الموجودة
- **حذف المنتجات**
- **البحث والفلترة** المتقدمة
- **إدارة المخزون** الحية
- **تصنيف المنتجات** والموردين

### 🔗 ASP.NET API Endpoints

#### المنتجات
- `GET /api/products` - جلب جميع المنتجات
- `GET /api/products/{id}` - جلب منتج محدد
- `GET /api/products/barcode/{barcode}` - البحث بالباركود
- `GET /api/products/category/{categoryId}` - منتجات حسب الفئة
- `GET /api/products/supplier/{supplierId}` - منتجات حسب المورد
- `POST /api/products` - إضافة منتج جديد
- `PUT /api/products/{id}` - تحديث منتج
- `DELETE /api/products/{id}` - حذف منتج

#### الفئات والموردين
- `GET /api/categories` - جلب جميع الفئات
- `GET /api/suppliers` - جلب جميع الموردين

#### أخرى
- `GET /api/dashboard` - إحصائيات لوحة التحكم
- Swagger UI متاح على: `https://localhost:7001/swagger`

## 📁 هيكل المشروع

```
ElectroHub Pro/
├── Frontend/
│   ├── dash.html              # الصفحة الرئيسية
│   ├── css/
│   │   └── dash-styles.css    # الأنماط المخصصة
│   └── js/
│       ├── dash-script.js     # الوظائف العامة
│       └── products-manager.js # إدارة المنتجات
├── Backend/
│   ├── app.py                 # الخادم الرئيسي
│   ├── routes/
│   │   └── products.py        # API المنتجات
│   ├── requirements.txt       # المكتبات المطلوبة
│   ├── run.bat               # ملف التشغيل
│   └── static/               # الملفات الثابتة
│       └── uploads/          # صور المنتجات
└── SETUP_GUIDE.md           # هذا الملف
```

## 🎨 الواجهات المتاحة

### 1. لوحة التحكم الرئيسية
- إحصائيات المبيعات والمخزون
- أحدث المعاملات
- الرسوم البيانية

### 2. إدارة المنتجات ⭐ (مربوطة بالـ Backend)
- جدول المنتجات التفاعلي
- إضافة/تعديل/حذف المنتجات
- رفع الصور
- البحث والفلترة

### 3. نقطة البيع (POS)
- واجهة بيع سريعة
- إدارة السلة
- معالجة المدفوعات

### 4. صفحات أخرى
- إدارة المخزون
- التقارير
- إدارة المستخدمين

## 🔧 التخصيص

### تغيير إعدادات API
في ملف `Frontend/js/products-manager.js`:
```javascript
this.apiBaseUrl = 'http://localhost:5000/api';
```

### إضافة فئات جديدة
يتم إدارة الفئات تلقائياً من قاعدة البيانات

### تخصيص الأنماط
عدل ملف `Frontend/css/dash-styles.css`

## 🐛 استكشاف الأخطاء

### مشكلة في تشغيل Backend
1. تأكد من تثبيت Python
2. تأكد من تثبيت المكتبات: `pip install -r requirements.txt`
3. تحقق من المنفذ 5000 غير مستخدم

### مشكلة في تحميل المنتجات
1. تأكد من تشغيل Backend على المنفذ 5000
2. تحقق من إعدادات CORS
3. افتح Developer Tools في المتصفح للتحقق من الأخطاء

### مشكلة في رفع الصور
1. تأكد من وجود مجلد `Backend/static/uploads/products`
2. تحقق من صيغة الصورة (PNG, JPG, JPEG, GIF, WEBP)
3. تأكد من حجم الصورة أقل من 16MB

## 📞 الدعم

للحصول على المساعدة:
1. تحقق من ملف SETUP_GUIDE
2. راجع رسائل الخطأ في Developer Tools
3. تأكد من تشغيل Backend بشكل صحيح

---

## 🎉 تم إنجاز ربط إدارة المنتجات بالكامل!

✅ **Frontend** - واجهة تفاعلية كاملة
✅ **Backend** - API متكامل مع قاعدة البيانات
✅ **Database** - SQLite مع جداول منظمة
✅ **File Upload** - رفع وإدارة صور المنتجات
✅ **CRUD Operations** - إضافة/تعديل/حذف/عرض
✅ **Search & Filter** - بحث وفلترة متقدمة
✅ **Pagination** - تقسيم النتائج للصفحات

## 🚀 الخطوات التالية

1. **تثبيت Python** إذا لم يكن مثبتاً
2. **تشغيل Backend** باستخدام `python Backend/app.py`
3. **فتح Frontend** في المتصفح
4. **اختبار إدارة المنتجات** - إضافة/تعديل/حذف
5. **ربط باقي الصفحات** بالـ Backend حسب الحاجة
