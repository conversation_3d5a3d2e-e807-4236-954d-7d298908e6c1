{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;", "SqlExpress": "Server=.\\SQLEXPRESS;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;", "LocalHost": "Server=localhost;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;", "SqlServer": "Server=.;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "ElectronicsStore_SuperSecretKey_2024_MustBe32CharactersOrMore_ForSecurity", "Issuer": "ElectronicsStore.API", "Audience": "ElectronicsStore.Client", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ClockSkewInSeconds": 0}}