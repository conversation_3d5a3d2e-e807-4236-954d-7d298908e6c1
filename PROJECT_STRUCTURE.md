# 🏗️ هيكلة مشروع نظام إدارة المتجر الإلكتروني - شرح شامل

## 📋 نظرة عامة على المشروع

**اسم المشروع**: Electronics Store Management System  
**النوع**: نظام إدارة متجر إلكتروني شامل  
**المعمارية**: Clean Architecture Pattern  
**التقنيات**: ASP.NET Core 8, Entity Framework Core, SQL Server, HTML/CSS/JavaScript  

---

## 🗂️ الهيكل العام للمشروع

```
Final_Project/
├── 📁 Backend/                          # الخادم الخلفي - ASP.NET Core
├── 📁 Frontend/                         # الواجهة الأمامية - HTML/CSS/JS
├── 📁 Database/                         # قاعدة البيانات - SQL Scripts
├── 📄 README.md                         # دليل المشروع
├── 📄 SETUP_GUIDE.md                    # دليل التشغيل
└── 🔧 ملفات التشغيل والإعداد
```

---

## 🔧 1. Backend - الخادم الخلفي

### 🏛️ معمارية Clean Architecture

```
┌─────────────────────────────────────┐
│     Presentation Layer (WebAPI)    │ ← طبقة العرض
├─────────────────────────────────────┤
│     Application Layer (Services)   │ ← طبقة التطبيق
├─────────────────────────────────────┤
│   Infrastructure Layer (Data)      │ ← طبقة البنية التحتية
├─────────────────────────────────────┤
│       Domain Layer (Entities)      │ ← طبقة النطاق
└─────────────────────────────────────┘
```

### 📁 ElectronicsStore.Domain - طبقة النطاق

**الغرض**: تحتوي على منطق الأعمال الأساسي والكيانات الرئيسية

```
ElectronicsStore.Domain/
├── 📁 Entities/                        # الكيانات الأساسية
│   ├── 📄 Product.cs                   # كيان المنتج
│   ├── 📄 Category.cs                  # كيان الصنف
│   ├── 📄 Supplier.cs                  # كيان المورد
│   ├── 📄 User.cs                      # كيان المستخدم
│   ├── 📄 PurchaseInvoice.cs           # فاتورة الشراء
│   ├── 📄 SalesInvoice.cs              # فاتورة البيع
│   ├── 📄 Inventory.cs                 # المخزون
│   └── 📄 Return.cs                    # المرتجعات
├── 📁 Enums/                           # التعدادات
│   ├── 📄 UserRole.cs                  # أدوار المستخدمين
│   ├── 📄 InvoiceStatus.cs             # حالة الفاتورة
│   └── 📄 ProductStatus.cs             # حالة المنتج
├── 📁 Interfaces/                      # واجهات المستودعات
│   ├── 📄 IProductRepository.cs        # واجهة مستودع المنتجات
│   ├── 📄 ICategoryRepository.cs       # واجهة مستودع الأصناف
│   └── 📄 IUnitOfWork.cs               # واجهة وحدة العمل
└── 📁 ValueObjects/                    # كائنات القيم
    ├── 📄 Money.cs                     # كائن المال
    └── 📄 Address.cs                   # كائن العنوان
```

**الخصائص الرئيسية**:
- ✅ لا تعتمد على أي طبقة أخرى
- ✅ تحتوي على منطق الأعمال الأساسي
- ✅ تعرف الكيانات والقواعد التجارية

### 📁 ElectronicsStore.Application - طبقة التطبيق

**الغرض**: تحتوي على منطق التطبيق وحالات الاستخدام

```
ElectronicsStore.Application/
├── 📁 DTOs/                            # كائنات نقل البيانات
│   ├── 📄 ProductDto.cs                # DTO المنتج
│   ├── 📄 CategoryDto.cs               # DTO الصنف
│   ├── 📄 SupplierDto.cs               # DTO المورد
│   ├── 📄 UserDto.cs                   # DTO المستخدم
│   └── 📄 InvoiceDto.cs                # DTO الفاتورة
├── 📁 Services/                        # خدمات التطبيق
│   ├── 📄 ProductService.cs            # خدمة المنتجات
│   ├── 📄 CategoryService.cs           # خدمة الأصناف
│   ├── 📄 SupplierService.cs           # خدمة الموردين
│   ├── 📄 UserService.cs               # خدمة المستخدمين
│   ├── 📄 PurchaseInvoiceService.cs    # خدمة فواتير الشراء
│   ├── 📄 SalesInvoiceService.cs       # خدمة فواتير البيع
│   ├── 📄 InventoryService.cs          # خدمة المخزون
│   ├── 📄 ReturnsService.cs            # خدمة المرتجعات
│   ├── 📄 ExpenseService.cs            # خدمة المصروفات
│   ├── 📄 JwtService.cs                # خدمة JWT
│   └── 📄 PasswordService.cs           # خدمة كلمات المرور
├── 📁 Interfaces/                      # واجهات الخدمات
│   ├── 📄 IProductService.cs           # واجهة خدمة المنتجات
│   ├── 📄 ICategoryService.cs          # واجهة خدمة الأصناف
│   └── 📄 IUserService.cs              # واجهة خدمة المستخدمين
├── 📁 UseCases/                        # حالات الاستخدام
│   ├── 📄 CreateProductUseCase.cs      # إنشاء منتج
│   ├── 📄 UpdateInventoryUseCase.cs    # تحديث المخزون
│   └── 📄 ProcessSaleUseCase.cs        # معالجة البيع
└── 📁 Models/                          # نماذج التطبيق
    ├── 📄 LoginModel.cs                # نموذج تسجيل الدخول
    ├── 📄 JwtSettings.cs               # إعدادات JWT
    └── 📄 ApiResponse.cs               # استجابة API
```

**الخصائص الرئيسية**:
- ✅ تحتوي على منطق التطبيق
- ✅ تنسق بين الطبقات المختلفة
- ✅ تحول البيانات باستخدام DTOs

### 📁 ElectronicsStore.Infrastructure - طبقة البنية التحتية

**الغرض**: تنفيذ الوصول للبيانات والخدمات الخارجية

```
ElectronicsStore.Infrastructure/
├── 📁 Data/                            # سياق قاعدة البيانات
│   └── 📄 ElectronicsStoreDbContext.cs # DbContext الرئيسي
├── 📁 Repositories/                    # تنفيذ المستودعات
│   ├── 📄 ProductRepository.cs         # مستودع المنتجات
│   ├── 📄 CategoryRepository.cs        # مستودع الأصناف
│   ├── 📄 SupplierRepository.cs        # مستودع الموردين
│   ├── 📄 UserRepository.cs            # مستودع المستخدمين
│   └── 📄 UnitOfWork.cs                # وحدة العمل
├── 📁 Configurations/                  # إعدادات Entity Framework
│   ├── 📄 ProductConfiguration.cs      # إعداد كيان المنتج
│   ├── 📄 CategoryConfiguration.cs     # إعداد كيان الصنف
│   └── 📄 UserConfiguration.cs         # إعداد كيان المستخدم
└── 📁 Migrations/                      # هجرات قاعدة البيانات
    ├── 📄 20240101_InitialCreate.cs    # الهجرة الأولى
    └── 📄 20240102_AddInventory.cs     # إضافة المخزون
```

**الخصائص الرئيسية**:
- ✅ تنفذ واجهات طبقة النطاق
- ✅ تتعامل مع قاعدة البيانات
- ✅ تحتوي على إعدادات Entity Framework

### 📁 ElectronicsStore.WebAPI - طبقة واجهة برمجة التطبيقات

**الغرض**: نقطة الدخول للنظام وتعريض APIs

```
ElectronicsStore.WebAPI/
├── 📁 Controllers/                     # متحكمات API
│   ├── 📄 ProductsController.cs        # متحكم المنتجات
│   ├── 📄 CategoriesController.cs      # متحكم الأصناف
│   ├── 📄 SuppliersController.cs       # متحكم الموردين
│   ├── 📄 UsersController.cs           # متحكم المستخدمين
│   ├── 📄 AuthController.cs            # متحكم المصادقة
│   ├── 📄 PurchaseInvoicesController.cs # متحكم فواتير الشراء
│   ├── 📄 SalesInvoicesController.cs   # متحكم فواتير البيع
│   ├── 📄 InventoryController.cs       # متحكم المخزون
│   └── 📄 DashboardController.cs       # متحكم لوحة التحكم
├── 📁 Middleware/                      # الوسطاء المخصصة
│   ├── 📄 ExceptionMiddleware.cs       # وسيط معالجة الأخطاء
│   └── 📄 JwtMiddleware.cs             # وسيط JWT
├── 📁 Extensions/                      # امتدادات الخدمات
│   └── 📄 ServiceExtensions.cs        # امتدادات تسجيل الخدمات
├── 📁 Properties/                      # خصائص المشروع
│   └── 📄 launchSettings.json          # إعدادات التشغيل
├── 📄 Program.cs                       # نقطة بداية التطبيق
├── 📄 appsettings.json                 # إعدادات التطبيق
└── 📄 appsettings.Development.json     # إعدادات التطوير
```

**الخصائص الرئيسية**:
- ✅ تعرض REST APIs
- ✅ تتعامل مع HTTP Requests/Responses
- ✅ تحتوي على middleware للمصادقة والأخطاء

### 📁 مشاريع الاختبار والأدوات

```
Backend/
├── 📁 ElectronicsStore.Test/           # اختبارات الوحدة
│   ├── 📄 Program.cs                   # نقطة بداية الاختبارات
│   └── 📄 ElectronicsStore.Test.csproj # ملف المشروع
├── 📁 CreateAdmin/                     # أداة إنشاء مدير النظام
│   ├── 📄 Program.cs                   # إنشاء مستخدم مدير
│   └── 📄 CreateAdmin.csproj           # ملف المشروع
└── 📁 SimpleTest/                      # اختبارات بسيطة
    ├── 📄 Program.cs                   # اختبارات أساسية
    └── 📄 SimpleTest.csproj            # ملف المشروع
```

### 📁 Python Flask API (إضافي)

```
Backend/
├── 📄 app.py                           # خادم Flask للاختبار
├── 📁 routes/                          # مسارات Python API
│   ├── 📄 __init__.py                  # ملف التهيئة
│   └── 📄 products.py                  # مسارات المنتجات
├── 📄 requirements.txt                 # المكتبات المطلوبة
├── 📄 expense.json                     # بيانات المصروفات التجريبية
├── 📄 login.json                       # بيانات تسجيل الدخول
└── 📄 run.bat                          # ملف تشغيل Python
```

---

## 🎨 2. Frontend - الواجهة الأمامية

### 📁 هيكل Frontend

```
Frontend/
├── 📄 login.html                       # صفحة تسجيل الدخول
├── 📄 dash.html                        # لوحة التحكم الرئيسية
├── 📄 dash1.html                       # لوحة تحكم بديلة
├── 📁 css/                             # ملفات الأنماط
│   ├── 📄 style.css                    # الأنماط العامة
│   └── 📄 dash-styles.css              # أنماط لوحة التحكم
└── 📁 js/                              # وحدات JavaScript
    ├── 📄 auth.js                      # إدارة المصادقة والتوكن
    ├── 📄 api.js                       # التواصل مع Backend APIs
    ├── 📄 dash-script.js               # وظائف لوحة التحكم العامة
    ├── 📄 products-manager.js          # إدارة المنتجات (CRUD)
    ├── 📄 inventory-manager.js         # إدارة المخزون
    ├── 📄 pos-manager.js               # نظام نقطة البيع
    ├── 📄 suppliers-manager.js         # إدارة الموردين
    ├── 📄 sales-storage.js             # تخزين بيانات المبيعات
    └── 📄 dash.html                    # ملف HTML إضافي
```

### 🎯 وظائف الوحدات

#### 📄 auth.js - إدارة المصادقة
```javascript
// الوظائف الرئيسية:
- login()                               // تسجيل الدخول
- logout()                              // تسجيل الخروج
- checkAuth()                           // فحص المصادقة
- getToken()                            // الحصول على التوكن
- refreshToken()                        // تجديد التوكن
```

#### 📄 api.js - التواصل مع APIs
```javascript
// الوظائف الرئيسية:
- makeRequest()                         // إرسال طلبات HTTP
- handleResponse()                      // معالجة الاستجابات
- handleError()                         // معالجة الأخطاء
- setAuthHeader()                       // تعيين رأس المصادقة
```

#### 📄 products-manager.js - إدارة المنتجات
```javascript
// الوظائف الرئيسية:
- loadProducts()                        // تحميل المنتجات
- addProduct()                          // إضافة منتج جديد
- updateProduct()                       // تحديث منتج
- deleteProduct()                       // حذف منتج
- searchProducts()                      // البحث في المنتجات
- filterByCategory()                    // الفلترة حسب الصنف
```

#### 📄 inventory-manager.js - إدارة المخزون
```javascript
// الوظائف الرئيسية:
- checkStock()                          // فحص المخزون
- updateStock()                         // تحديث المخزون
- lowStockAlert()                       // تنبيه المخزون المنخفض
- stockReport()                         // تقرير المخزون
```

#### 📄 pos-manager.js - نقطة البيع
```javascript
// الوظائف الرئيسية:
- addToCart()                           // إضافة للسلة
- removeFromCart()                      // إزالة من السلة
- calculateTotal()                      // حساب الإجمالي
- processPayment()                      // معالجة الدفع
- printReceipt()                        // طباعة الفاتورة
```

---

## 🗄️ 3. Database - قاعدة البيانات

### 📁 هيكل قاعدة البيانات

```
Database/
├── 📄 DatabaseSchema.sql               # المخطط الأساسي للجداول
├── 📄 DatabaseSchema_Part2.sql         # الجزء الثاني من المخطط
└── 📄 DatabaseViews.sql                # العروض والاستعلامات المعقدة
```

### 🏷️ الجداول الرئيسية

#### 📊 جداول البيانات الأساسية
```sql
-- جدول الأصناف
Categories
├── CategoryId (PK)                     -- معرف الصنف
├── CategoryName                        -- اسم الصنف
├── Description                         -- الوصف
└── IsActive                            -- حالة النشاط

-- جدول الموردين
Suppliers
├── SupplierId (PK)                     -- معرف المورد
├── SupplierName                        -- اسم المورد
├── ContactInfo                         -- معلومات الاتصال
├── Address                             -- العنوان
└── IsActive                            -- حالة النشاط

-- جدول المنتجات
Products
├── ProductId (PK)                      -- معرف المنتج
├── ProductName                         -- اسم المنتج
├── CategoryId (FK)                     -- معرف الصنف
├── SupplierId (FK)                     -- معرف المورد
├── Barcode                             -- الباركود
├── PurchasePrice                       -- سعر الشراء
├── SalePrice                           -- سعر البيع
├── StockQuantity                       -- كمية المخزون
├── MinStockLevel                       -- الحد الأدنى للمخزون
├── ImagePath                           -- مسار الصورة
├── Description                         -- الوصف
├── IsActive                            -- حالة النشاط
├── CreatedDate                         -- تاريخ الإنشاء
└── LastModified                        -- تاريخ آخر تعديل
```

#### 👥 جداول المستخدمين والأدوار
```sql
-- جدول المستخدمين
Users
├── UserId (PK)                         -- معرف المستخدم
├── Username                            -- اسم المستخدم
├── Email                               -- البريد الإلكتروني
├── PasswordHash                        -- كلمة المرور المشفرة
├── Role                                -- الدور
├── IsActive                            -- حالة النشاط
├── CreatedDate                         -- تاريخ الإنشاء
└── LastLogin                           -- آخر تسجيل دخول
```
