﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Infrastructure.Data;
using ElectronicsStore.Infrastructure.Repositories;
using ElectronicsStore.Domain.Interfaces;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Application.Models;
using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Test;

public class Program
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🧪 اختبار JWT Authentication System");
        Console.WriteLine("=====================================");

        // إعداد الخدمات
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .Build();

        // إضافة DbContext
        services.AddDbContext<ElectronicsStoreDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("LocalHost")));

        // إضافة الخدمات
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IPasswordService, PasswordService>();
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IJwtService, JwtService>();
        services.Configure<JwtSettings>(configuration.GetSection("JwtSettings"));

        var serviceProvider = services.BuildServiceProvider();

        try
        {
            // اختبار الاتصال بقاعدة البيانات
            Console.WriteLine("🔍 اختبار الاتصال بقاعدة البيانات...");
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ElectronicsStoreDbContext>();

            var canConnect = await dbContext.Database.CanConnectAsync();
            if (canConnect)
            {
                Console.WriteLine("✅ الاتصال بقاعدة البيانات ناجح");
            }
            else
            {
                Console.WriteLine("❌ فشل الاتصال بقاعدة البيانات");
                return;
            }

            // اختبار الخدمات
            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
            var passwordService = scope.ServiceProvider.GetRequiredService<IPasswordService>();
            var jwtService = scope.ServiceProvider.GetRequiredService<IJwtService>();

            Console.WriteLine("\n🔐 اختبار Password Service...");

            // اختبار تشفير كلمة المرور
            var password = "Ahmed123!";
            var hashedPassword = passwordService.HashPassword(password);
            var isValid = passwordService.VerifyPassword(password, hashedPassword);
            var strength = passwordService.CheckPasswordStrength(password);

            Console.WriteLine($"✅ تشفير كلمة المرور: {hashedPassword.Substring(0, 20)}...");
            Console.WriteLine($"✅ التحقق من كلمة المرور: {isValid}");
            Console.WriteLine($"✅ قوة كلمة المرور: {strength}");

            Console.WriteLine("\n👥 اختبار User Service...");

            // التحقق من وجود المستخدمين
            var allUsers = await userService.GetAllUsersAsync();
            Console.WriteLine($"✅ عدد المستخدمين الموجودين: {allUsers.Count()}");

            // إنشاء Super Admin Ahmed إذا لم يكن موجود
            var adminUser = await userService.GetUserByUsernameAsync("Ahmed");
            if (adminUser == null)
            {
                Console.WriteLine("\n🔧 إنشاء Super Admin Ahmed...");

                var createAdminDto = new CreateUserDto
                {
                    Username = "Ahmed",
                    Email = "<EMAIL>",
                    Password = "Ahmed123!",
                    FullName = "Ahmed Super Admin",
                    PhoneNumber = "+966501234567",
                    RoleId = 1, // Admin role
                    IsActive = true
                };

                try
                {
                    adminUser = await userService.CreateUserAsync(createAdminDto);
                    Console.WriteLine($"✅ تم إنشاء Super Admin Ahmed بنجاح - ID: {adminUser.Id}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ فشل في إنشاء Super Admin: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine($"✅ Super Admin Ahmed موجود بالفعل - ID: {adminUser.Id}");
            }

            if (adminUser != null)
            {
                Console.WriteLine("\n🔑 اختبار JWT Service...");

                // إنشاء JWT Token
                var token = jwtService.GenerateToken(adminUser);
                var refreshToken = jwtService.GenerateRefreshToken();

                Console.WriteLine($"✅ تم إنشاء JWT Token: {token.Substring(0, 50)}...");
                Console.WriteLine($"✅ تم إنشاء Refresh Token: {refreshToken.Substring(0, 20)}...");

                // التحقق من Token
                var isTokenValid = jwtService.ValidateToken(token);
                var userId = jwtService.GetUserIdFromToken(token);
                var username = jwtService.GetUsernameFromToken(token);
                var role = jwtService.GetUserRoleFromToken(token);
                var permissions = jwtService.GetUserPermissionsFromToken(token);
                var expirationDate = jwtService.GetTokenExpirationDate(token);

                Console.WriteLine($"✅ صحة Token: {isTokenValid}");
                Console.WriteLine($"✅ User ID من Token: {userId}");
                Console.WriteLine($"✅ Username من Token: {username}");
                Console.WriteLine($"✅ Role من Token: {role}");
                Console.WriteLine($"✅ عدد الصلاحيات: {permissions.Count}");
                Console.WriteLine($"✅ تاريخ انتهاء الصلاحية: {expirationDate}");

                Console.WriteLine("\n📊 ملخص الاختبار:");
                Console.WriteLine("==================");
                Console.WriteLine("✅ قاعدة البيانات: متصلة");
                Console.WriteLine("✅ Password Service: يعمل");
                Console.WriteLine("✅ User Service: يعمل");
                Console.WriteLine("✅ JWT Service: يعمل");
                Console.WriteLine("✅ Super Admin Ahmed: جاهز");
                Console.WriteLine("\n🎉 النظام جاهز للاستخدام!");

                Console.WriteLine("\n🔐 بيانات تسجيل الدخول:");
                Console.WriteLine($"Username: {adminUser.Username}");
                Console.WriteLine($"Email: {adminUser.Email}");
                Console.WriteLine("Password: Ahmed123!");
                Console.WriteLine($"Role: {adminUser.RoleName}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
            Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
        }
        finally
        {
            serviceProvider.Dispose();
        }
    }
}
