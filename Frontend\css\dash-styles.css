/* ===== ElectroHub Pro Dashboard Styles ===== */

/* Font Import */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* Global Styles */
* {
    font-family: 'Cairo', sans-serif;
}

/* Sidebar Styles */
.sidebar-item {
    transition: all 0.3s ease;
}

.sidebar-item:hover {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    transform: translateX(-2px);
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    transform: translateY(-1px);
}

/* Input Focus Styles */
.input-focus {
    transition: all 0.3s ease;
}

.input-focus:focus {
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.pulse-dot {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
    }
    50% { 
        opacity: 0.5; 
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from { 
        opacity: 0; 
        transform: scale(0.9) translateY(-20px); 
    }
    to { 
        opacity: 1; 
        transform: scale(1) translateY(0); 
    }
}

/* Tab Styles */
.tab-active {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    color: white;
}

/* Progress Bar */
.progress-bar {
    background: linear-gradient(90deg, #0ea5e9 0%, #0284c7 100%);
    height: 4px;
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* Page Content */
.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #0ea5e9;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0284c7;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
}

@media (max-width: 768px) {
    .grid-cols-4 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .grid-cols-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .text-3xl {
        font-size: 1.875rem;
    }
}

@media (max-width: 480px) {
    .grid-cols-4,
    .grid-cols-3,
    .grid-cols-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    
    .text-3xl {
        font-size: 1.5rem;
    }
    
    .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
    
    .bg-white {
        background: white !important;
    }
    
    .shadow-sm,
    .shadow-lg {
        box-shadow: none !important;
    }
}

/* Loading Spinner */
.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0ea5e9;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert-success {
    background-color: #d1fae5;
    border-color: #a7f3d0;
    color: #065f46;
}

.alert-error {
    background-color: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-warning {
    background-color: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
}

.alert-info {
    background-color: #dbeafe;
    border-color: #bfdbfe;
    color: #1e40af;
}

/* Custom Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Status Badges */
.status-available {
    background-color: #d1fae5;
    color: #065f46;
}

.status-low-stock {
    background-color: #fef3c7;
    color: #92400e;
}

.status-out-of-stock {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Chart Styles */
.chart-bar {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    border-radius: 4px 4px 0 0;
    transition: all 0.3s ease;
}

.chart-bar:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    transform: scaleY(1.05);
}
