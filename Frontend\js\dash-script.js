// ===== ElectroHub Pro Dashboard JavaScript =====

// Global Variables
let cart = [];
let currentPage = 'dashboard';

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    loadDashboardData();
});

// Initialize Dashboard
function initializeDashboard() {
    console.log('🚀 ElectroHub Pro Dashboard Initialized');
    
    // Set active page
    showPage('dashboard');
    
    // Initialize tooltips
    initializeTooltips();
    
    // Load user preferences
    loadUserPreferences();
}

// Setup Event Listeners
function setupEventListeners() {
    // Sidebar navigation
    document.querySelectorAll('.sidebar-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page');
            if (page) {
                showPage(page);
                setActiveNavItem(this);
            }
        });
    });
    
    // Modal close buttons
    document.querySelectorAll('.modal .close-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal.id);
        });
    });
    
    // Click outside modal to close
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal(this.id);
            }
        });
    });
    
    // Search functionality
    const searchInput = document.querySelector('input[placeholder="البحث السريع..."]');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
}

// Page Navigation
function showPage(pageId) {
    // Hide all pages
    document.querySelectorAll('.page-content').forEach(page => {
        page.classList.remove('active');
        page.classList.add('hidden');
    });
    
    // Show selected page
    const targetPage = document.getElementById(pageId + '-page');
    if (targetPage) {
        targetPage.classList.remove('hidden');
        targetPage.classList.add('active');
        currentPage = pageId;
        
        // Update page title
        updatePageTitle(pageId);
        
        // Load page-specific data
        loadPageData(pageId);
    }
}

// Set Active Navigation Item
function setActiveNavItem(activeItem) {
    // Remove active class from all items
    document.querySelectorAll('.sidebar-item').forEach(item => {
        item.classList.remove('bg-primary-50', 'text-primary-700');
        item.classList.add('text-gray-700');
    });
    
    // Add active class to clicked item
    activeItem.classList.remove('text-gray-700');
    activeItem.classList.add('bg-primary-50', 'text-primary-700');
}

// Modal Functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Focus first input
        const firstInput = modal.querySelector('input, select, textarea');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
        
        // Reset form if exists
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }
}

// POS Functions
function addToCart(productName, price) {
    const existingItem = cart.find(item => item.name === productName);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            name: productName,
            price: price,
            quantity: 1
        });
    }
    
    updateCartDisplay();
    showNotification('تم إضافة المنتج إلى السلة', 'success');
}

function removeFromCart(productName) {
    cart = cart.filter(item => item.name !== productName);
    updateCartDisplay();
    showNotification('تم حذف المنتج من السلة', 'info');
}

function updateCartDisplay() {
    const cartContainer = document.getElementById('cart-items');
    const totalElement = document.getElementById('cart-total');
    
    if (!cartContainer || !totalElement) return;
    
    // Clear cart display
    cartContainer.innerHTML = '';
    
    let total = 0;
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        const cartItem = document.createElement('div');
        cartItem.className = 'flex items-center justify-between p-3 border-b border-gray-200';
        cartItem.innerHTML = `
            <div class="flex-1">
                <h4 class="font-medium text-gray-900">${item.name}</h4>
                <p class="text-sm text-gray-500">${item.price} ر.س × ${item.quantity}</p>
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                <button onclick="updateQuantity('${item.name}', ${item.quantity - 1})" 
                        class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                    </svg>
                </button>
                <span class="w-8 text-center">${item.quantity}</span>
                <button onclick="updateQuantity('${item.name}', ${item.quantity + 1})" 
                        class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </button>
                <button onclick="removeFromCart('${item.name}')" 
                        class="text-red-600 hover:text-red-800 mr-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
        `;
        
        cartContainer.appendChild(cartItem);
    });
    
    totalElement.textContent = `${total.toLocaleString()} ر.س`;
}

function updateQuantity(productName, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(productName);
        return;
    }
    
    const item = cart.find(item => item.name === productName);
    if (item) {
        item.quantity = newQuantity;
        updateCartDisplay();
    }
}

function clearCart() {
    cart = [];
    updateCartDisplay();
    showNotification('تم مسح السلة', 'info');
}

function processPayment() {
    if (cart.length === 0) {
        showNotification('السلة فارغة', 'warning');
        return;
    }
    
    // Simulate payment processing
    showNotification('جاري معالجة الدفع...', 'info');
    
    setTimeout(() => {
        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        showNotification(`تم إتمام البيع بنجاح - المجموع: ${total.toLocaleString()} ر.س`, 'success');
        
        // Clear cart and close modal
        clearCart();
        closeModal('paymentModal');
        
        // Print receipt (optional)
        printReceipt();
    }, 2000);
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function handleSearch(event) {
    const query = event.target.value.toLowerCase();
    console.log('Searching for:', query);
    // Implement search logic here
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;
    
    // Set notification style based on type
    switch (type) {
        case 'success':
            notification.classList.add('bg-green-500', 'text-white');
            break;
        case 'error':
            notification.classList.add('bg-red-500', 'text-white');
            break;
        case 'warning':
            notification.classList.add('bg-yellow-500', 'text-white');
            break;
        default:
            notification.classList.add('bg-blue-500', 'text-white');
    }
    
    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

// Load Dashboard Data
function loadDashboardData() {
    // Simulate loading data
    console.log('Loading dashboard data...');
    
    // Update stats with animation
    animateCounters();
}

function animateCounters() {
    const counters = document.querySelectorAll('[data-counter]');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-counter'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString();
        }, 16);
    });
}

// Page-specific data loading
function loadPageData(pageId) {
    switch (pageId) {
        case 'products':
            loadProductsData();
            break;
        case 'inventory':
            loadInventoryData();
            break;
        case 'reports':
            loadReportsData();
            break;
        default:
            break;
    }
}

function loadProductsData() {
    console.log('Loading products data...');
    // Implement products data loading
}

function loadInventoryData() {
    console.log('Loading inventory data...');
    // Implement inventory data loading
}

function loadReportsData() {
    console.log('Loading reports data...');
    // Implement reports data loading
}

// Initialize tooltips and other UI components
function initializeTooltips() {
    // Add tooltip functionality if needed
}

function loadUserPreferences() {
    // Load user preferences from localStorage
    const preferences = localStorage.getItem('electrohub-preferences');
    if (preferences) {
        const prefs = JSON.parse(preferences);
        // Apply preferences
        console.log('User preferences loaded:', prefs);
    }
}

function updatePageTitle(pageId) {
    const titles = {
        'dashboard': 'لوحة المعلومات',
        'products': 'إدارة المنتجات',
        'inventory': 'المخزون',
        'pos': 'نقطة البيع',
        'suppliers': 'الموردين',
        'returns': 'المرتجعات',
        'reports': 'التقارير',
        'users': 'المستخدمين'
    };
    
    document.title = `${titles[pageId] || 'ElectroHub Pro'} - ElectroHub Pro`;
}

function printReceipt() {
    // Implement receipt printing
    console.log('Printing receipt...');
}
