// ===== Users Management System =====

class UsersManager {
    constructor() {
        this.users = [];
        this.roles = [];
        this.currentEditingUser = null;
        this.apiService = new ApiService();
        
        this.init();
    }

    // Initialize the users manager
    init() {
        console.log('🔧 Initializing Users Manager...');
        
        // Load initial data
        this.loadUsers();
        this.loadRoles();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('✅ Users Manager initialized');
    }

    // Setup event listeners
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('userSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.filterUsers(e.target.value);
            }, 300));
        }

        // Role filter
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.filterUsersByRole(e.target.value);
            });
        }

        // Add user form
        const addUserForm = document.querySelector('#addUserModal form');
        if (addUserForm) {
            addUserForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddUser(e);
            });
        }
    }

    // Load users from API
    async loadUsers() {
        try {
            this.showLoading(true);
            console.log('👥 Loading users...');
            
            this.users = await this.apiService.get('/users');
            console.log('👥 Loaded users:', this.users.length);
            
            this.renderUsers();
            this.updateUsersStats();
            
        } catch (error) {
            console.error('Error loading users:', error);
            this.showNotification('خطأ في تحميل المستخدمين: ' + error.message, 'error');
            this.renderEmptyState();
        } finally {
            this.showLoading(false);
        }
    }

    // Load roles from API
    async loadRoles() {
        try {
            // For now, use static roles - can be replaced with API call later
            this.roles = [
                { id: 1, name: 'مدير', nameEn: 'Admin' },
                { id: 2, name: 'موظف مبيعات', nameEn: 'Sales' },
                { id: 3, name: 'مسؤول مخزون', nameEn: 'Inventory' }
            ];
            
            this.populateRoleSelects();
            
        } catch (error) {
            console.error('Error loading roles:', error);
        }
    }

    // Populate role select elements
    populateRoleSelects() {
        const roleSelects = document.querySelectorAll('select[name="roleId"], #roleFilter');
        
        roleSelects.forEach(select => {
            if (select.id === 'roleFilter') {
                // Keep the "جميع الأدوار" option for filter
                const options = select.querySelectorAll('option:not(:first-child)');
                options.forEach(option => option.remove());
            } else {
                // Clear all options for role selects in forms
                select.innerHTML = '<option value="">اختر الدور</option>';
            }
            
            this.roles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.name;
                select.appendChild(option);
            });
        });
    }

    // Render users table
    renderUsers(usersToRender = null) {
        const users = usersToRender || this.users;
        const tbody = document.querySelector('#users-page tbody');
        
        if (!tbody) return;

        if (users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <p class="text-lg font-medium">لا توجد مستخدمين</p>
                            <p class="text-sm">ابدأ بإضافة مستخدم جديد</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = users.map(user => `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <img src="${this.generateAvatar(user.fullName || user.username)}" alt="${user.fullName || user.username}" class="w-10 h-10 rounded-full">
                        <div>
                            <div class="text-sm font-medium text-gray-900">${user.fullName || user.username}</div>
                            <div class="text-sm text-gray-500">${user.roleName}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${this.getRoleBadgeClass(user.roleName)}">
                        ${user.roleName}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.email}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatLastLogin(user.lastLoginAt)}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${user.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button class="text-primary-600 hover:text-primary-900" onclick="window.usersManager.editUser(${user.id})">تعديل</button>
                        <button class="text-blue-600 hover:text-blue-900" onclick="window.usersManager.viewUserActivity(${user.id})">النشاط</button>
                        <button class="text-orange-600 hover:text-orange-900" onclick="window.usersManager.resetPassword(${user.id})">إعادة تعيين كلمة المرور</button>
                        ${user.isActive ? 
                            `<button class="text-red-600 hover:text-red-900" onclick="window.usersManager.deactivateUser(${user.id})">إلغاء التفعيل</button>` :
                            `<button class="text-green-600 hover:text-green-900" onclick="window.usersManager.activateUser(${user.id})">تفعيل</button>`
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // Generate avatar SVG
    generateAvatar(name) {
        const firstLetter = name ? name.charAt(0).toUpperCase() : 'U';
        const colors = ['#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
        const color = colors[name.length % colors.length];
        
        return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='${encodeURIComponent(color)}'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16' font-weight='bold'%3E${encodeURIComponent(firstLetter)}%3C/text%3E%3C/svg%3E`;
    }

    // Get role badge CSS class
    getRoleBadgeClass(roleName) {
        switch (roleName) {
            case 'مدير':
                return 'bg-purple-100 text-purple-800';
            case 'موظف مبيعات':
                return 'bg-green-100 text-green-800';
            case 'مسؤول مخزون':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    // Format last login time
    formatLastLogin(lastLoginAt) {
        if (!lastLoginAt) return 'لم يسجل دخول';
        
        const now = new Date();
        const loginDate = new Date(lastLoginAt);
        const diffMs = now - loginDate;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);
        
        if (diffMins < 1) return 'الآن';
        if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
        if (diffHours < 24) return `منذ ${diffHours} ساعة`;
        return `منذ ${diffDays} يوم`;
    }

    // Update users statistics
    updateUsersStats() {
        const totalUsers = this.users.length;
        const activeUsers = this.users.filter(u => u.isActive).length;
        const adminUsers = this.users.filter(u => u.roleName === 'مدير').length;
        const salesUsers = this.users.filter(u => u.roleName === 'موظف مبيعات').length;
        
        // Update stats cards
        const statsCards = document.querySelectorAll('#users-page .grid .bg-white');
        if (statsCards.length >= 4) {
            statsCards[0].querySelector('.text-2xl').textContent = totalUsers;
            statsCards[0].querySelector('.text-sm.text-blue-600').textContent = `${activeUsers} مستخدمين نشطين`;
            
            statsCards[1].querySelector('.text-2xl').textContent = adminUsers;
            statsCards[2].querySelector('.text-2xl').textContent = salesUsers;
            
            // Update last login info
            const recentUser = this.users
                .filter(u => u.lastLoginAt)
                .sort((a, b) => new Date(b.lastLoginAt) - new Date(a.lastLoginAt))[0];
                
            if (recentUser) {
                statsCards[3].querySelector('.text-lg').textContent = recentUser.fullName || recentUser.username;
                statsCards[3].querySelector('.text-sm.text-gray-600').textContent = this.formatLastLogin(recentUser.lastLoginAt);
            }
        }
    }

    // Filter users by search term
    filterUsers(searchTerm) {
        if (!searchTerm.trim()) {
            this.renderUsers();
            return;
        }
        
        const filtered = this.users.filter(user => 
            (user.fullName && user.fullName.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (user.username && user.username.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()))
        );
        
        this.renderUsers(filtered);
    }

    // Filter users by role
    filterUsersByRole(roleName) {
        if (!roleName || roleName === 'جميع الأدوار') {
            this.renderUsers();
            return;
        }
        
        const filtered = this.users.filter(user => user.roleName === roleName);
        this.renderUsers(filtered);
    }

    // Handle add user form submission
    async handleAddUser(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const userData = {
            username: formData.get('username')?.trim(),
            email: formData.get('email')?.trim(),
            fullName: formData.get('fullName')?.trim(),
            phoneNumber: formData.get('phoneNumber')?.trim(),
            roleId: parseInt(formData.get('roleId')),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword'),
            isActive: true
        };

        // Validate form data
        if (!this.validateUserData(userData)) {
            return;
        }

        try {
            this.showLoading(true);
            
            // Remove confirmPassword before sending to API
            const { confirmPassword, ...apiData } = userData;
            
            const newUser = await this.apiService.post('/users', apiData);
            
            this.users.push(newUser);
            this.renderUsers();
            this.updateUsersStats();
            
            this.showNotification('تم إضافة المستخدم بنجاح', 'success');
            this.closeModal('addUserModal');
            event.target.reset();
            
        } catch (error) {
            console.error('Error adding user:', error);
            this.showNotification('خطأ في إضافة المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Validate user data
    validateUserData(userData) {
        if (!userData.username) {
            this.showNotification('يرجى إدخال اسم المستخدم', 'error');
            return false;
        }
        
        if (!userData.email) {
            this.showNotification('يرجى إدخال البريد الإلكتروني', 'error');
            return false;
        }
        
        if (!userData.roleId) {
            this.showNotification('يرجى اختيار الدور', 'error');
            return false;
        }
        
        if (!userData.password) {
            this.showNotification('يرجى إدخال كلمة المرور', 'error');
            return false;
        }
        
        if (userData.password !== userData.confirmPassword) {
            this.showNotification('كلمة المرور وتأكيدها غير متطابقتين', 'error');
            return false;
        }
        
        if (userData.password.length < 6) {
            this.showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
            return false;
        }
        
        // Check if username already exists
        if (this.users.some(u => u.username.toLowerCase() === userData.username.toLowerCase())) {
            this.showNotification('اسم المستخدم موجود بالفعل', 'error');
            return false;
        }
        
        // Check if email already exists
        if (this.users.some(u => u.email.toLowerCase() === userData.email.toLowerCase())) {
            this.showNotification('البريد الإلكتروني موجود بالفعل', 'error');
            return false;
        }
        
        return true;
    }

    // Edit user
    async editUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        this.currentEditingUser = user;
        this.openEditUserModal(user);
    }

    // Open edit user modal
    openEditUserModal(user) {
        // Create edit modal if it doesn't exist
        if (!document.getElementById('editUserModal')) {
            this.createEditUserModal();
        }
        
        // Populate form with user data
        const form = document.querySelector('#editUserModal form');
        if (form) {
            form.querySelector('[name="fullName"]').value = user.fullName || '';
            form.querySelector('[name="username"]').value = user.username || '';
            form.querySelector('[name="email"]').value = user.email || '';
            form.querySelector('[name="phoneNumber"]').value = user.phoneNumber || '';
            form.querySelector('[name="roleId"]').value = user.roleId || '';
            form.querySelector('[name="isActive"]').checked = user.isActive;
        }
        
        this.openModal('editUserModal');
    }

    // View user activity
    async viewUserActivity(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        // Create activity modal if it doesn't exist
        if (!document.getElementById('userActivityModal')) {
            this.createUserActivityModal();
        }
        
        // Load and display user activity
        try {
            this.showLoading(true);
            
            // For now, show placeholder data - can be replaced with actual API call
            const activities = [
                { action: 'تسجيل دخول', timestamp: new Date(), details: 'تسجيل دخول من IP: *************' },
                { action: 'إضافة منتج', timestamp: new Date(Date.now() - 3600000), details: 'إضافة منتج: iPhone 15' },
                { action: 'تحديث مخزون', timestamp: new Date(Date.now() - 7200000), details: 'تحديث كمية منتج: Samsung Galaxy' }
            ];
            
            this.renderUserActivity(user, activities);
            this.openModal('userActivityModal');
            
        } catch (error) {
            console.error('Error loading user activity:', error);
            this.showNotification('خطأ في تحميل نشاط المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Reset user password
    async resetPassword(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        if (!confirm(`هل أنت متأكد من إعادة تعيين كلمة المرور للمستخدم "${user.fullName || user.username}"؟`)) {
            return;
        }
        
        try {
            this.showLoading(true);
            
            await this.apiService.put(`/users/${userId}/reset-password`);
            
            this.showNotification('تم إعادة تعيين كلمة المرور بنجاح', 'success');
            
        } catch (error) {
            console.error('Error resetting password:', error);
            this.showNotification('خطأ في إعادة تعيين كلمة المرور: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Activate user
    async activateUser(userId) {
        try {
            this.showLoading(true);
            
            await this.apiService.put(`/users/${userId}/activate`);
            
            // Update local data
            const user = this.users.find(u => u.id === userId);
            if (user) {
                user.isActive = true;
            }
            
            this.renderUsers();
            this.updateUsersStats();
            this.showNotification('تم تفعيل المستخدم بنجاح', 'success');
            
        } catch (error) {
            console.error('Error activating user:', error);
            this.showNotification('خطأ في تفعيل المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Deactivate user
    async deactivateUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        if (!confirm(`هل أنت متأكد من إلغاء تفعيل المستخدم "${user.fullName || user.username}"؟`)) {
            return;
        }
        
        try {
            this.showLoading(true);
            
            await this.apiService.put(`/users/${userId}/deactivate`);
            
            // Update local data
            user.isActive = false;
            
            this.renderUsers();
            this.updateUsersStats();
            this.showNotification('تم إلغاء تفعيل المستخدم بنجاح', 'success');
            
        } catch (error) {
            console.error('Error deactivating user:', error);
            this.showNotification('خطأ في إلغاء تفعيل المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Utility functions
    showLoading(show) {
        const loadingElement = document.getElementById('usersLoading');
        if (loadingElement) {
            loadingElement.style.display = show ? 'block' : 'none';
        }
    }

    showNotification(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    openModal(modalId) {
        if (typeof openModal === 'function') {
            openModal(modalId);
        } else {
            document.getElementById(modalId)?.classList.add('active');
        }
    }

    closeModal(modalId) {
        if (typeof closeModal === 'function') {
            closeModal(modalId);
        } else {
            document.getElementById(modalId)?.classList.remove('active');
        }
    }

    renderEmptyState() {
        this.renderUsers([]);
    }

    // Create edit user modal
    createEditUserModal() {
        const modalHTML = `
            <div id="editUserModal" class="modal">
                <div class="modal-content w-full max-w-2xl">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">تعديل المستخدم</h2>
                        <button onclick="window.usersManager.closeModal('editUserModal')" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form class="space-y-4" onsubmit="window.usersManager.handleEditUser(event)">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                                <input type="text" name="fullName" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم *</label>
                                <input type="text" name="username" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus bg-gray-50" readonly>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                                <input type="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                                <input type="tel" name="phoneNumber" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الدور *</label>
                                <select name="roleId" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                                    <option value="">اختر الدور</option>
                                </select>
                            </div>
                            <div class="flex items-center">
                                <label class="flex items-center">
                                    <input type="checkbox" name="isActive" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="mr-2 text-sm text-gray-700">المستخدم نشط</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                            <button type="button" onclick="window.usersManager.closeModal('editUserModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                إلغاء
                            </button>
                            <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.populateRoleSelects(); // Populate role options in the new modal
    }

    // Create user activity modal
    createUserActivityModal() {
        const modalHTML = `
            <div id="userActivityModal" class="modal">
                <div class="modal-content w-full max-w-4xl">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">نشاط المستخدم</h2>
                        <button onclick="window.usersManager.closeModal('userActivityModal')" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div id="userActivityContent">
                        <!-- Activity content will be loaded here -->
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // Handle edit user form submission
    async handleEditUser(event) {
        event.preventDefault();

        if (!this.currentEditingUser) {
            this.showNotification('خطأ: لم يتم تحديد المستخدم للتعديل', 'error');
            return;
        }

        const formData = new FormData(event.target);
        const userData = {
            email: formData.get('email')?.trim(),
            fullName: formData.get('fullName')?.trim(),
            phoneNumber: formData.get('phoneNumber')?.trim(),
            roleId: parseInt(formData.get('roleId')),
            isActive: formData.has('isActive')
        };

        try {
            this.showLoading(true);

            const updatedUser = await this.apiService.put(`/users/${this.currentEditingUser.id}`, userData);

            // Update local data
            const index = this.users.findIndex(u => u.id === this.currentEditingUser.id);
            if (index !== -1) {
                this.users[index] = updatedUser;
            }

            this.renderUsers();
            this.updateUsersStats();

            this.showNotification('تم تحديث المستخدم بنجاح', 'success');
            this.closeModal('editUserModal');
            this.currentEditingUser = null;

        } catch (error) {
            console.error('Error updating user:', error);
            this.showNotification('خطأ في تحديث المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Render user activity
    renderUserActivity(user, activities) {
        const content = document.getElementById('userActivityContent');
        if (!content) return;

        content.innerHTML = `
            <div class="mb-6">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <img src="${this.generateAvatar(user.fullName || user.username)}" alt="${user.fullName || user.username}" class="w-12 h-12 rounded-full">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">${user.fullName || user.username}</h3>
                        <p class="text-sm text-gray-500">${user.email} • ${user.roleName}</p>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="text-md font-medium text-gray-900">آخر الأنشطة</h4>
                <div class="space-y-3">
                    ${activities.map(activity => `
                        <div class="flex items-start space-x-3 space-x-reverse p-4 bg-gray-50 rounded-lg">
                            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">${activity.action}</p>
                                <p class="text-sm text-gray-500">${activity.details}</p>
                                <p class="text-xs text-gray-400 mt-1">${this.formatLastLogin(activity.timestamp)}</p>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Global functions for backward compatibility
function editUser(userId) {
    if (window.usersManager) {
        window.usersManager.editUser(userId);
    }
}

function viewUserActivity(userId) {
    if (window.usersManager) {
        window.usersManager.viewUserActivity(userId);
    }
}

function resetPassword(userId) {
    if (window.usersManager) {
        window.usersManager.resetPassword(userId);
    }
}
