// ===== Users Management System =====

class UsersManager {
    constructor() {
        this.users = [];
        this.roles = [];
        this.currentEditingUser = null;
        this.selectedUsers = new Set();
        this.bulkActionsEnabled = false;
        this.apiService = new ApiService();

        this.init();
    }

    // Initialize the users manager
    init() {
        console.log('🔧 Initializing Users Manager...');
        
        // Load initial data
        this.loadUsers();
        this.loadRoles();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('✅ Users Manager initialized');
    }

    // Setup event listeners
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('userSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.filterUsers(e.target.value);
            }, 300));
        }

        // Role filter
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.filterUsersByRole(e.target.value);
            });
        }

        // Add user form
        const addUserForm = document.querySelector('#addUserModal form');
        if (addUserForm) {
            addUserForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddUser(e);
            });
        }

        // Advanced filters form
        const filtersForm = document.getElementById('advancedFiltersForm');
        if (filtersForm) {
            filtersForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.applyAdvancedFilters(e);
            });
        }
    }

    // Load users from API
    async loadUsers() {
        try {
            this.showLoading(true);
            console.log('👥 Loading users...');

            // Check if user is authenticated
            if (!window.isAuthenticated || !window.isAuthenticated()) {
                console.log('🚫 User not authenticated, redirecting to login');
                window.location.href = 'login.html';
                return;
            }

            this.users = await this.apiService.get('/users');
            console.log('👥 Loaded users:', this.users.length);

            this.renderUsers();
            this.updateUsersStats();

        } catch (error) {
            console.error('Error loading users:', error);

            // Handle authentication errors
            if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                console.log('🔒 Authentication failed, redirecting to login');
                this.showNotification('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى', 'error');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
                return;
            }

            this.showNotification('خطأ في تحميل المستخدمين، سيتم عرض بيانات تجريبية', 'warning');
            this.loadDemoUsers();
        } finally {
            this.showLoading(false);
        }
    }

    // Load roles from API
    async loadRoles() {
        try {
            // For now, use static roles - can be replaced with API call later
            this.roles = [
                { id: 1, name: 'مدير', nameEn: 'Admin' },
                { id: 2, name: 'موظف مبيعات', nameEn: 'Sales' },
                { id: 3, name: 'مسؤول مخزون', nameEn: 'Inventory' }
            ];

            this.populateRoleSelects();

        } catch (error) {
            console.error('Error loading roles:', error);
        }
    }

    // Load demo users for testing
    loadDemoUsers() {
        console.log('📋 Loading demo users...');

        this.users = [
            {
                id: 1,
                username: 'admin',
                email: '<EMAIL>',
                fullName: 'أحمد محمد الإداري',
                phoneNumber: '+966501234567',
                roleId: 1,
                roleName: 'مدير',
                isActive: true,
                createdAt: '2024-01-15T10:30:00Z',
                lastLoginAt: new Date(Date.now() - 300000).toISOString() // 5 minutes ago
            },
            {
                id: 2,
                username: 'sara.sales',
                email: '<EMAIL>',
                fullName: 'سارة أحمد المبيعات',
                phoneNumber: '+966507654321',
                roleId: 2,
                roleName: 'موظف مبيعات',
                isActive: true,
                createdAt: '2024-02-01T14:20:00Z',
                lastLoginAt: new Date(Date.now() - 1800000).toISOString() // 30 minutes ago
            },
            {
                id: 3,
                username: 'omar.inventory',
                email: '<EMAIL>',
                fullName: 'عمر خالد المخزون',
                phoneNumber: '+966509876543',
                roleId: 3,
                roleName: 'مسؤول مخزون',
                isActive: true,
                createdAt: '2024-01-20T09:15:00Z',
                lastLoginAt: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
            },
            {
                id: 4,
                username: 'fatima.sales',
                email: '<EMAIL>',
                fullName: 'فاطمة علي المبيعات',
                phoneNumber: '+966502468135',
                roleId: 2,
                roleName: 'موظف مبيعات',
                isActive: false,
                createdAt: '2024-03-01T11:45:00Z',
                lastLoginAt: new Date(Date.now() - 86400000).toISOString() // 1 day ago
            },
            {
                id: 5,
                username: 'khalid.admin',
                email: '<EMAIL>',
                fullName: 'خالد سعد الإداري',
                phoneNumber: '+966503691472',
                roleId: 1,
                roleName: 'مدير',
                isActive: true,
                createdAt: '2024-01-10T08:00:00Z',
                lastLoginAt: new Date(Date.now() - 7200000).toISOString() // 2 hours ago
            },
            {
                id: 6,
                username: 'noor.inventory',
                email: '<EMAIL>',
                fullName: 'نور محمد المخزون',
                phoneNumber: '+966508529637',
                roleId: 3,
                roleName: 'مسؤول مخزون',
                isActive: true,
                createdAt: '2024-02-15T13:30:00Z',
                lastLoginAt: new Date(Date.now() - 10800000).toISOString() // 3 hours ago
            }
        ];

        console.log('📋 Demo users loaded:', this.users.length);
        this.renderUsers();
        this.updateUsersStats();

        // Show demo mode notification
        setTimeout(() => {
            this.showNotification('تم تحميل بيانات تجريبية للمستخدمين. للحصول على البيانات الحقيقية، تأكد من تسجيل الدخول والاتصال بالخادم', 'info');
        }, 1000);
    }

    // Populate role select elements
    populateRoleSelects() {
        const roleSelects = document.querySelectorAll('select[name="roleId"], #roleFilter');
        
        roleSelects.forEach(select => {
            if (select.id === 'roleFilter') {
                // Keep the "جميع الأدوار" option for filter
                const options = select.querySelectorAll('option:not(:first-child)');
                options.forEach(option => option.remove());
            } else {
                // Clear all options for role selects in forms
                select.innerHTML = '<option value="">اختر الدور</option>';
            }
            
            this.roles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.name;
                select.appendChild(option);
            });
        });
    }

    // Render users table
    renderUsers(usersToRender = null) {
        const users = usersToRender || this.users;
        const tbody = document.querySelector('#users-page tbody');
        
        if (!tbody) return;

        if (users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <p class="text-lg font-medium">لا توجد مستخدمين</p>
                            <p class="text-sm">ابدأ بإضافة مستخدم جديد</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = users.map(user => `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        ${this.bulkActionsEnabled ?
                            `<input type="checkbox" name="userSelect" value="${user.id}" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" onchange="window.usersManager.toggleUserSelection(${user.id}, this.checked)">` :
                            ''
                        }
                        <img src="${this.generateAvatar(user.fullName || user.username)}" alt="${user.fullName || user.username}" class="w-10 h-10 rounded-full">
                        <div>
                            <div class="text-sm font-medium text-gray-900">${user.fullName || user.username}</div>
                            <div class="text-sm text-gray-500">${user.roleName}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${this.getRoleBadgeClass(user.roleName)}">
                        ${user.roleName}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.email}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.phoneNumber || 'غير محدد'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.createdAt ? new Date(user.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatLastLogin(user.lastLoginAt)}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${user.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                        <div class="w-2 h-2 rounded-full ${this.isUserOnline(user) ? 'bg-green-500' : 'bg-gray-300'}" title="${this.isUserOnline(user) ? 'متصل' : 'غير متصل'}"></div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button class="text-primary-600 hover:text-primary-900" onclick="window.usersManager.editUser(${user.id})">تعديل</button>
                        <button class="text-blue-600 hover:text-blue-900" onclick="window.usersManager.viewUserActivity(${user.id})">النشاط</button>
                        <button class="text-orange-600 hover:text-orange-900" onclick="window.usersManager.resetPassword(${user.id})">إعادة تعيين كلمة المرور</button>
                        ${user.isActive ?
                            `<button class="text-red-600 hover:text-red-900" onclick="window.usersManager.deactivateUser(${user.id})">إلغاء التفعيل</button>` :
                            `<button class="text-green-600 hover:text-green-900" onclick="window.usersManager.activateUser(${user.id})">تفعيل</button>`
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // Generate avatar SVG
    generateAvatar(name) {
        const firstLetter = name ? name.charAt(0).toUpperCase() : 'U';
        const colors = ['#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
        const color = colors[name.length % colors.length];
        
        return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='${encodeURIComponent(color)}'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16' font-weight='bold'%3E${encodeURIComponent(firstLetter)}%3C/text%3E%3C/svg%3E`;
    }

    // Get role badge CSS class
    getRoleBadgeClass(roleName) {
        switch (roleName) {
            case 'مدير':
                return 'bg-purple-100 text-purple-800';
            case 'موظف مبيعات':
                return 'bg-green-100 text-green-800';
            case 'مسؤول مخزون':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    // Format last login time
    formatLastLogin(lastLoginAt) {
        if (!lastLoginAt) return 'لم يسجل دخول';
        
        const now = new Date();
        const loginDate = new Date(lastLoginAt);
        const diffMs = now - loginDate;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);
        
        if (diffMins < 1) return 'الآن';
        if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
        if (diffHours < 24) return `منذ ${diffHours} ساعة`;
        return `منذ ${diffDays} يوم`;
    }

    // Update users statistics
    updateUsersStats() {
        const totalUsers = this.users.length;
        const activeUsers = this.users.filter(u => u.isActive).length;
        const adminUsers = this.users.filter(u => u.roleName === 'مدير').length;
        const salesUsers = this.users.filter(u => u.roleName === 'موظف مبيعات').length;
        
        // Update stats cards
        const statsCards = document.querySelectorAll('#users-page .grid .bg-white');
        if (statsCards.length >= 4) {
            statsCards[0].querySelector('.text-2xl').textContent = totalUsers;
            statsCards[0].querySelector('.text-sm.text-blue-600').textContent = `${activeUsers} مستخدمين نشطين`;
            
            statsCards[1].querySelector('.text-2xl').textContent = adminUsers;
            statsCards[2].querySelector('.text-2xl').textContent = salesUsers;
            
            // Update last login info
            const recentUser = this.users
                .filter(u => u.lastLoginAt)
                .sort((a, b) => new Date(b.lastLoginAt) - new Date(a.lastLoginAt))[0];
                
            if (recentUser) {
                statsCards[3].querySelector('.text-lg').textContent = recentUser.fullName || recentUser.username;
                statsCards[3].querySelector('.text-sm.text-gray-600').textContent = this.formatLastLogin(recentUser.lastLoginAt);
            }
        }
    }

    // Filter users by search term
    filterUsers(searchTerm) {
        if (!searchTerm.trim()) {
            this.renderUsers();
            return;
        }
        
        const filtered = this.users.filter(user => 
            (user.fullName && user.fullName.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (user.username && user.username.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()))
        );
        
        this.renderUsers(filtered);
    }

    // Filter users by role
    filterUsersByRole(roleName) {
        if (!roleName || roleName === 'جميع الأدوار') {
            this.renderUsers();
            return;
        }
        
        const filtered = this.users.filter(user => user.roleName === roleName);
        this.renderUsers(filtered);
    }

    // Handle add user form submission
    async handleAddUser(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const userData = {
            username: formData.get('username')?.trim(),
            email: formData.get('email')?.trim(),
            fullName: formData.get('fullName')?.trim(),
            phoneNumber: formData.get('phoneNumber')?.trim(),
            roleId: parseInt(formData.get('roleId')) || 2, // Default to Sales role if not specified
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword'),
            isActive: true
        };

        // Validate form data
        if (!this.validateUserData(userData)) {
            return;
        }

        try {
            this.showLoading(true);
            
            // Remove confirmPassword before sending to API
            const { confirmPassword, ...apiData } = userData;
            
            const newUser = await this.apiService.post('/users', apiData);
            
            this.users.push(newUser);
            this.renderUsers();
            this.updateUsersStats();
            
            this.showNotification('تم إضافة المستخدم بنجاح', 'success');
            this.closeModal('addUserModal');
            event.target.reset();
            
        } catch (error) {
            console.error('Error adding user:', error);

            // Handle authentication errors
            if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                this.showNotification('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى', 'error');
                setTimeout(() => window.location.href = 'login.html', 2000);
                return;
            }

            // Handle validation errors (400 Bad Request)
            if (error.message.includes('400') || error.message.includes('Bad Request')) {
                this.showNotification('خطأ في البيانات المدخلة. تأكد من صحة جميع الحقول المطلوبة', 'error');
                console.log('Validation error details:', error);
                return;
            }

            this.showNotification('خطأ في إضافة المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Validate user data
    validateUserData(userData) {
        // Username validation
        if (!userData.username || userData.username.length < 3) {
            this.showNotification('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
            return false;
        }

        // Username format validation (only letters, numbers, and underscore)
        if (!/^[a-zA-Z0-9_]+$/.test(userData.username)) {
            this.showNotification('اسم المستخدم يجب أن يحتوي على أحرف وأرقام و _ فقط', 'error');
            return false;
        }

        // Email validation
        if (!userData.email) {
            this.showNotification('يرجى إدخال البريد الإلكتروني', 'error');
            return false;
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(userData.email)) {
            this.showNotification('البريد الإلكتروني غير صحيح', 'error');
            return false;
        }

        // Role validation
        if (!userData.roleId || userData.roleId < 1) {
            this.showNotification('يرجى اختيار الدور', 'error');
            return false;
        }

        // Password validation
        if (!userData.password) {
            this.showNotification('يرجى إدخال كلمة المرور', 'error');
            return false;
        }

        if (userData.password.length < 6) {
            this.showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
            return false;
        }

        if (userData.password !== userData.confirmPassword) {
            this.showNotification('كلمة المرور وتأكيدها غير متطابقتين', 'error');
            return false;
        }

        // Phone number validation (if provided)
        if (userData.phoneNumber && userData.phoneNumber.trim()) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,20}$/;
            if (!phoneRegex.test(userData.phoneNumber.trim())) {
                this.showNotification('رقم الهاتف غير صحيح', 'error');
                return false;
            }
        }

        // Check if username already exists (only in demo mode)
        if (this.users.some(u => u.username.toLowerCase() === userData.username.toLowerCase())) {
            this.showNotification('اسم المستخدم موجود بالفعل', 'error');
            return false;
        }

        // Check if email already exists (only in demo mode)
        if (this.users.some(u => u.email.toLowerCase() === userData.email.toLowerCase())) {
            this.showNotification('البريد الإلكتروني موجود بالفعل', 'error');
            return false;
        }

        return true;
    }

    // Edit user
    async editUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        this.currentEditingUser = user;
        this.openEditUserModal(user);
    }

    // Open edit user modal
    openEditUserModal(user) {
        // Create edit modal if it doesn't exist
        if (!document.getElementById('editUserModal')) {
            this.createEditUserModal();
        }
        
        // Populate form with user data
        const form = document.querySelector('#editUserModal form');
        if (form) {
            form.querySelector('[name="fullName"]').value = user.fullName || '';
            form.querySelector('[name="username"]').value = user.username || '';
            form.querySelector('[name="email"]').value = user.email || '';
            form.querySelector('[name="phoneNumber"]').value = user.phoneNumber || '';
            form.querySelector('[name="roleId"]').value = user.roleId || '';
            form.querySelector('[name="isActive"]').checked = user.isActive;
        }
        
        this.openModal('editUserModal');
    }

    // View user activity
    async viewUserActivity(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        // Create activity modal if it doesn't exist
        if (!document.getElementById('userActivityModal')) {
            this.createUserActivityModal();
        }
        
        // Load and display user activity
        try {
            this.showLoading(true);
            
            // For now, show placeholder data - can be replaced with actual API call
            const activities = [
                { action: 'تسجيل دخول', timestamp: new Date(), details: 'تسجيل دخول من IP: *************' },
                { action: 'إضافة منتج', timestamp: new Date(Date.now() - 3600000), details: 'إضافة منتج: iPhone 15' },
                { action: 'تحديث مخزون', timestamp: new Date(Date.now() - 7200000), details: 'تحديث كمية منتج: Samsung Galaxy' }
            ];
            
            this.renderUserActivity(user, activities);
            this.openModal('userActivityModal');
            
        } catch (error) {
            console.error('Error loading user activity:', error);
            this.showNotification('خطأ في تحميل نشاط المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Reset user password
    async resetPassword(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        if (!confirm(`هل أنت متأكد من إعادة تعيين كلمة المرور للمستخدم "${user.fullName || user.username}"؟`)) {
            return;
        }
        
        try {
            this.showLoading(true);
            
            await this.apiService.put(`/users/${userId}/reset-password`);
            
            this.showNotification('تم إعادة تعيين كلمة المرور بنجاح', 'success');
            
        } catch (error) {
            console.error('Error resetting password:', error);
            this.showNotification('خطأ في إعادة تعيين كلمة المرور: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Activate user
    async activateUser(userId) {
        try {
            this.showLoading(true);
            
            await this.apiService.put(`/users/${userId}/activate`);
            
            // Update local data
            const user = this.users.find(u => u.id === userId);
            if (user) {
                user.isActive = true;
            }
            
            this.renderUsers();
            this.updateUsersStats();
            this.showNotification('تم تفعيل المستخدم بنجاح', 'success');
            
        } catch (error) {
            console.error('Error activating user:', error);
            this.showNotification('خطأ في تفعيل المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Deactivate user
    async deactivateUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        if (!confirm(`هل أنت متأكد من إلغاء تفعيل المستخدم "${user.fullName || user.username}"؟`)) {
            return;
        }
        
        try {
            this.showLoading(true);
            
            await this.apiService.put(`/users/${userId}/deactivate`);
            
            // Update local data
            user.isActive = false;
            
            this.renderUsers();
            this.updateUsersStats();
            this.showNotification('تم إلغاء تفعيل المستخدم بنجاح', 'success');
            
        } catch (error) {
            console.error('Error deactivating user:', error);
            this.showNotification('خطأ في إلغاء تفعيل المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Utility functions
    showLoading(show) {
        const loadingElement = document.getElementById('usersLoading');
        if (loadingElement) {
            loadingElement.style.display = show ? 'block' : 'none';
        }
    }

    showNotification(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    openModal(modalId) {
        if (typeof openModal === 'function') {
            openModal(modalId);
        } else {
            document.getElementById(modalId)?.classList.add('active');
        }
    }

    closeModal(modalId) {
        if (typeof closeModal === 'function') {
            closeModal(modalId);
        } else {
            document.getElementById(modalId)?.classList.remove('active');
        }
    }

    renderEmptyState() {
        this.renderUsers([]);
    }

    // Create edit user modal
    createEditUserModal() {
        const modalHTML = `
            <div id="editUserModal" class="modal">
                <div class="modal-content w-full max-w-2xl">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">تعديل المستخدم</h2>
                        <button onclick="window.usersManager.closeModal('editUserModal')" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form class="space-y-4" onsubmit="window.usersManager.handleEditUser(event)">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                                <input type="text" name="fullName" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم *</label>
                                <input type="text" name="username" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus bg-gray-50" readonly>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                                <input type="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                                <input type="tel" name="phoneNumber" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الدور *</label>
                                <select name="roleId" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                                    <option value="">اختر الدور</option>
                                </select>
                            </div>
                            <div class="flex items-center">
                                <label class="flex items-center">
                                    <input type="checkbox" name="isActive" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="mr-2 text-sm text-gray-700">المستخدم نشط</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                            <button type="button" onclick="window.usersManager.closeModal('editUserModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                إلغاء
                            </button>
                            <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.populateRoleSelects(); // Populate role options in the new modal
    }

    // Create user activity modal
    createUserActivityModal() {
        const modalHTML = `
            <div id="userActivityModal" class="modal">
                <div class="modal-content w-full max-w-4xl">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">نشاط المستخدم</h2>
                        <button onclick="window.usersManager.closeModal('userActivityModal')" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div id="userActivityContent">
                        <!-- Activity content will be loaded here -->
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // Handle edit user form submission
    async handleEditUser(event) {
        event.preventDefault();

        if (!this.currentEditingUser) {
            this.showNotification('خطأ: لم يتم تحديد المستخدم للتعديل', 'error');
            return;
        }

        const formData = new FormData(event.target);
        const userData = {
            email: formData.get('email')?.trim(),
            fullName: formData.get('fullName')?.trim(),
            phoneNumber: formData.get('phoneNumber')?.trim(),
            roleId: parseInt(formData.get('roleId')),
            isActive: formData.has('isActive')
        };

        try {
            this.showLoading(true);

            const updatedUser = await this.apiService.put(`/users/${this.currentEditingUser.id}`, userData);

            // Update local data
            const index = this.users.findIndex(u => u.id === this.currentEditingUser.id);
            if (index !== -1) {
                this.users[index] = updatedUser;
            }

            this.renderUsers();
            this.updateUsersStats();

            this.showNotification('تم تحديث المستخدم بنجاح', 'success');
            this.closeModal('editUserModal');
            this.currentEditingUser = null;

        } catch (error) {
            console.error('Error updating user:', error);
            this.showNotification('خطأ في تحديث المستخدم: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Render user activity
    renderUserActivity(user, activities) {
        const content = document.getElementById('userActivityContent');
        if (!content) return;

        content.innerHTML = `
            <div class="mb-6">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <img src="${this.generateAvatar(user.fullName || user.username)}" alt="${user.fullName || user.username}" class="w-12 h-12 rounded-full">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">${user.fullName || user.username}</h3>
                        <p class="text-sm text-gray-500">${user.email} • ${user.roleName}</p>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="text-md font-medium text-gray-900">آخر الأنشطة</h4>
                <div class="space-y-3">
                    ${activities.map(activity => `
                        <div class="flex items-start space-x-3 space-x-reverse p-4 bg-gray-50 rounded-lg">
                            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">${activity.action}</p>
                                <p class="text-sm text-gray-500">${activity.details}</p>
                                <p class="text-xs text-gray-400 mt-1">${this.formatLastLogin(activity.timestamp)}</p>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Export users to CSV
    exportUsers() {
        try {
            const csvContent = this.generateCSV(this.users);
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `users_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showNotification('تم تصدير قائمة المستخدمين بنجاح', 'success');
        } catch (error) {
            console.error('Error exporting users:', error);
            this.showNotification('خطأ في تصدير المستخدمين', 'error');
        }
    }

    // Generate CSV content
    generateCSV(users) {
        const headers = ['الاسم الكامل', 'اسم المستخدم', 'البريد الإلكتروني', 'رقم الهاتف', 'الدور', 'الحالة', 'تاريخ التسجيل', 'آخر تسجيل دخول'];
        const csvRows = [headers.join(',')];

        users.forEach(user => {
            const row = [
                user.fullName || '',
                user.username || '',
                user.email || '',
                user.phoneNumber || '',
                user.roleName || '',
                user.isActive ? 'نشط' : 'غير نشط',
                user.createdAt ? new Date(user.createdAt).toLocaleDateString('ar-SA') : '',
                user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('ar-SA') : 'لم يسجل دخول'
            ];
            csvRows.push(row.map(field => `"${field}"`).join(','));
        });

        return csvRows.join('\n');
    }

    // Toggle bulk actions
    toggleBulkActions() {
        this.bulkActionsEnabled = !this.bulkActionsEnabled;
        const checkbox = document.getElementById('selectAllUsers');
        const bulkButton = document.querySelector('[onclick*="toggleBulkActions"]');

        if (this.bulkActionsEnabled) {
            checkbox.classList.remove('hidden');
            bulkButton.classList.add('bg-primary-100', 'text-primary-700');
            bulkButton.classList.remove('bg-gray-100', 'text-gray-700');
        } else {
            checkbox.classList.add('hidden');
            checkbox.checked = false;
            bulkButton.classList.remove('bg-primary-100', 'text-primary-700');
            bulkButton.classList.add('bg-gray-100', 'text-gray-700');
            this.selectedUsers.clear();
            this.updateBulkActionsUI();
        }
    }

    // Toggle select all users
    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('input[name="userSelect"]');
        checkboxes.forEach(cb => {
            cb.checked = checked;
            if (checked) {
                this.selectedUsers.add(parseInt(cb.value));
            } else {
                this.selectedUsers.delete(parseInt(cb.value));
            }
        });
        this.updateBulkActionsUI();
    }

    // Update bulk actions UI
    updateBulkActionsUI() {
        const count = this.selectedUsers.size;
        const countElement = document.getElementById('selectedUsersCount');
        if (countElement) {
            countElement.textContent = count;
        }

        if (count > 0) {
            this.openModal('bulkActionsModal');
        }
    }

    // Bulk activate users
    async bulkActivate() {
        if (this.selectedUsers.size === 0) return;

        if (!confirm(`هل أنت متأكد من تفعيل ${this.selectedUsers.size} مستخدم؟`)) {
            return;
        }

        try {
            this.showLoading(true);

            const promises = Array.from(this.selectedUsers).map(userId =>
                this.apiService.put(`/users/${userId}/activate`)
            );

            await Promise.all(promises);

            // Update local data
            this.users.forEach(user => {
                if (this.selectedUsers.has(user.id)) {
                    user.isActive = true;
                }
            });

            this.renderUsers();
            this.updateUsersStats();
            this.selectedUsers.clear();
            this.closeModal('bulkActionsModal');

            this.showNotification('تم تفعيل المستخدمين بنجاح', 'success');

        } catch (error) {
            console.error('Error bulk activating users:', error);
            this.showNotification('خطأ في تفعيل المستخدمين: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Bulk deactivate users
    async bulkDeactivate() {
        if (this.selectedUsers.size === 0) return;

        if (!confirm(`هل أنت متأكد من إلغاء تفعيل ${this.selectedUsers.size} مستخدم؟`)) {
            return;
        }

        try {
            this.showLoading(true);

            const promises = Array.from(this.selectedUsers).map(userId =>
                this.apiService.put(`/users/${userId}/deactivate`)
            );

            await Promise.all(promises);

            // Update local data
            this.users.forEach(user => {
                if (this.selectedUsers.has(user.id)) {
                    user.isActive = false;
                }
            });

            this.renderUsers();
            this.updateUsersStats();
            this.selectedUsers.clear();
            this.closeModal('bulkActionsModal');

            this.showNotification('تم إلغاء تفعيل المستخدمين بنجاح', 'success');

        } catch (error) {
            console.error('Error bulk deactivating users:', error);
            this.showNotification('خطأ في إلغاء تفعيل المستخدمين: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Bulk reset passwords
    async bulkResetPassword() {
        if (this.selectedUsers.size === 0) return;

        if (!confirm(`هل أنت متأكد من إعادة تعيين كلمات المرور لـ ${this.selectedUsers.size} مستخدم؟`)) {
            return;
        }

        try {
            this.showLoading(true);

            const promises = Array.from(this.selectedUsers).map(userId =>
                this.apiService.put(`/users/${userId}/reset-password`)
            );

            await Promise.all(promises);

            this.selectedUsers.clear();
            this.closeModal('bulkActionsModal');

            this.showNotification('تم إعادة تعيين كلمات المرور بنجاح', 'success');

        } catch (error) {
            console.error('Error bulk resetting passwords:', error);
            this.showNotification('خطأ في إعادة تعيين كلمات المرور: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Bulk export selected users
    bulkExport() {
        if (this.selectedUsers.size === 0) return;

        const selectedUsersData = this.users.filter(user => this.selectedUsers.has(user.id));
        const csvContent = this.generateCSV(selectedUsersData);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `selected_users_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showNotification('تم تصدير المستخدمين المحددين بنجاح', 'success');
        this.closeModal('bulkActionsModal');
    }

    // Refresh active sessions
    async refreshActiveSessions() {
        try {
            // This would typically call an API endpoint for active sessions
            // For now, we'll show a placeholder
            this.showNotification('تم تحديث الجلسات النشطة', 'success');
        } catch (error) {
            console.error('Error refreshing active sessions:', error);
            this.showNotification('خطأ في تحديث الجلسات النشطة', 'error');
        }
    }

    // Clear filters
    clearFilters() {
        const form = document.getElementById('advancedFiltersForm');
        if (form) {
            form.reset();
            this.renderUsers(); // Show all users
            this.showNotification('تم مسح جميع الفلاتر', 'success');
        }
    }

    // Toggle user selection for bulk actions
    toggleUserSelection(userId, checked) {
        if (checked) {
            this.selectedUsers.add(userId);
        } else {
            this.selectedUsers.delete(userId);
        }

        // Update select all checkbox
        const selectAllCheckbox = document.getElementById('selectAllUsers');
        const allCheckboxes = document.querySelectorAll('input[name="userSelect"]');
        const checkedCheckboxes = document.querySelectorAll('input[name="userSelect"]:checked');

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
        }

        this.updateBulkActionsUI();
    }

    // Check if user is online (placeholder logic)
    isUserOnline(user) {
        // This would typically check against real-time session data
        // For now, we'll use a simple heuristic based on last login
        if (!user.lastLoginAt) return false;

        const lastLogin = new Date(user.lastLoginAt);
        const now = new Date();
        const diffMinutes = (now - lastLogin) / (1000 * 60);

        return diffMinutes < 30; // Consider online if logged in within last 30 minutes
    }

    // Apply advanced filters
    applyAdvancedFilters(event) {
        const formData = new FormData(event.target);
        const filters = {
            registrationDateFrom: formData.get('registrationDateFrom'),
            registrationDateTo: formData.get('registrationDateTo'),
            lastLoginFrom: formData.get('lastLoginFrom'),
            lastLoginTo: formData.get('lastLoginTo'),
            userStatus: formData.get('userStatus'),
            connectionStatus: formData.get('connectionStatus'),
            roleFilter: formData.get('roleFilter'),
            permissions: formData.getAll('permissions')
        };

        let filteredUsers = [...this.users];

        // Apply date filters
        if (filters.registrationDateFrom) {
            const fromDate = new Date(filters.registrationDateFrom);
            filteredUsers = filteredUsers.filter(user =>
                user.createdAt && new Date(user.createdAt) >= fromDate
            );
        }

        if (filters.registrationDateTo) {
            const toDate = new Date(filters.registrationDateTo);
            toDate.setHours(23, 59, 59, 999); // End of day
            filteredUsers = filteredUsers.filter(user =>
                user.createdAt && new Date(user.createdAt) <= toDate
            );
        }

        if (filters.lastLoginFrom) {
            const fromDate = new Date(filters.lastLoginFrom);
            filteredUsers = filteredUsers.filter(user =>
                user.lastLoginAt && new Date(user.lastLoginAt) >= fromDate
            );
        }

        if (filters.lastLoginTo) {
            const toDate = new Date(filters.lastLoginTo);
            toDate.setHours(23, 59, 59, 999);
            filteredUsers = filteredUsers.filter(user =>
                user.lastLoginAt && new Date(user.lastLoginAt) <= toDate
            );
        }

        // Apply status filters
        if (filters.userStatus) {
            const isActive = filters.userStatus === 'active';
            filteredUsers = filteredUsers.filter(user => user.isActive === isActive);
        }

        if (filters.connectionStatus) {
            const shouldBeOnline = filters.connectionStatus === 'online';
            filteredUsers = filteredUsers.filter(user => this.isUserOnline(user) === shouldBeOnline);
        }

        if (filters.roleFilter) {
            filteredUsers = filteredUsers.filter(user => user.roleId == filters.roleFilter);
        }

        // Apply permissions filter (this would need more complex logic in a real app)
        if (filters.permissions.length > 0) {
            filteredUsers = filteredUsers.filter(user => {
                // Placeholder logic - in reality, you'd check user permissions
                return true;
            });
        }

        this.renderUsers(filteredUsers);
        this.closeModal('userFiltersModal');

        const activeFiltersCount = Object.values(filters).filter(v =>
            Array.isArray(v) ? v.length > 0 : v && v.trim() !== ''
        ).length;

        this.showNotification(`تم تطبيق ${activeFiltersCount} فلتر. عرض ${filteredUsers.length} من ${this.users.length} مستخدم`, 'success');
    }

    // Handle authentication errors
    handleAuthError(error) {
        if (error.message.includes('401') || error.message.includes('Unauthorized')) {
            console.log('🔒 Authentication error detected');
            this.showNotification('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى', 'error');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
            return true;
        }
        return false;
    }

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Global functions for backward compatibility
function editUser(userId) {
    if (window.usersManager) {
        window.usersManager.editUser(userId);
    }
}

function viewUserActivity(userId) {
    if (window.usersManager) {
        window.usersManager.viewUserActivity(userId);
    }
}

function resetPassword(userId) {
    if (window.usersManager) {
        window.usersManager.resetPassword(userId);
    }
}
