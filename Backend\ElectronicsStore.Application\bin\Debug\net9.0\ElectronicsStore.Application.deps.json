{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"ElectronicsStore.Application/1.0.0": {"dependencies": {"ElectronicsStore.Domain": "1.0.0", "Microsoft.Extensions.Options": "9.0.8", "System.IdentityModel.Tokens.Jwt": "8.14.0"}, "runtime": {"ElectronicsStore.Application.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.IdentityModel.Abstractions/8.14.0": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.14.0.0", "fileVersion": "8.14.0.60815"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.14.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.14.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.14.0.0", "fileVersion": "8.14.0.60815"}}}, "Microsoft.IdentityModel.Logging/8.14.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.14.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.14.0.0", "fileVersion": "8.14.0.60815"}}}, "Microsoft.IdentityModel.Tokens/8.14.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.IdentityModel.Logging": "8.14.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.14.0.0", "fileVersion": "8.14.0.60815"}}}, "System.IdentityModel.Tokens.Jwt/8.14.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.14.0", "Microsoft.IdentityModel.Tokens": "8.14.0"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.14.0.0", "fileVersion": "8.14.0.60815"}}}, "ElectronicsStore.Domain/1.0.0": {"runtime": {"ElectronicsStore.Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"ElectronicsStore.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "path": "microsoft.extensions.options/9.0.8", "hashPath": "microsoft.extensions.options.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-iwbCpSjD3ehfTwBhtSNEtKPK0ICun6ov7Ibx6ISNA9bfwIyzI2Siwyi9eJFCJBwxowK9xcA1mj+jBWiigeqgcQ==", "path": "microsoft.identitymodel.abstractions/8.14.0", "hashPath": "microsoft.identitymodel.abstractions.8.14.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-4jOpiA4THdtpLyMdAb24dtj7+6GmvhOhxf5XHLYWmPKF8ApEnApal1UnJsKO4HxUWRXDA6C4WQVfYyqsRhpNpQ==", "path": "microsoft.identitymodel.jsonwebtokens/8.14.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.14.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-eqqnemdW38CKZEHS6diA50BV94QICozDZEvSrsvN3SJXUFwVB9gy+/oz76gldP7nZliA16IglXjXTCTdmU/Ejg==", "path": "microsoft.identitymodel.logging/8.14.0", "hashPath": "microsoft.identitymodel.logging.8.14.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-lKIZiBiGd36k02TCdMHp1KlNWisyIvQxcYJvIkz7P4gSQ9zi8dgh6S5Grj8NNG7HWYIPfQymGyoZ6JB5d1Lo1g==", "path": "microsoft.identitymodel.tokens/8.14.0", "hashPath": "microsoft.identitymodel.tokens.8.14.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-EYGgN/S+HK7S6F3GaaPLFAfK0UzMrkXFyWCvXpQWFYmZln3dqtbyIO7VuTM/iIIPMzkelg8ZLlBPvMhxj6nOAA==", "path": "system.identitymodel.tokens.jwt/8.14.0", "hashPath": "system.identitymodel.tokens.jwt.8.14.0.nupkg.sha512"}, "ElectronicsStore.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}