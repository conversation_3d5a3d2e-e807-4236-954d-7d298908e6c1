﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Infrastructure.Data;
using ElectronicsStore.Infrastructure.Repositories;
using ElectronicsStore.Domain.Interfaces;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Application.DTOs;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🧪 اختبار بسيط للنظام");
        Console.WriteLine("===================");

        try
        {
            // إعداد الخدمات
            var services = new ServiceCollection();

            // إضافة DbContext مع connection string مباشر
            var connectionString = "Server=localhost;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;";
            services.AddDbContext<ElectronicsStoreDbContext>(options =>
                options.UseSqlServer(connectionString));

            // إضافة الخدمات
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IPasswordService, PasswordService>();
            services.AddScoped<IUserService, UserService>();

            var serviceProvider = services.BuildServiceProvider();

            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ElectronicsStoreDbContext>();

            Console.WriteLine("🔍 اختبار الاتصال بقاعدة البيانات...");
            var canConnect = await dbContext.Database.CanConnectAsync();

            if (canConnect)
            {
                Console.WriteLine("✅ الاتصال بقاعدة البيانات ناجح");

                // اختبار الخدمات
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
                var passwordService = scope.ServiceProvider.GetRequiredService<IPasswordService>();

                Console.WriteLine("\n🔐 اختبار Password Service...");
                var password = "Ahmed123!";
                var hashedPassword = passwordService.HashPassword(password);
                var isValid = passwordService.VerifyPassword(password, hashedPassword);

                Console.WriteLine($"✅ تشفير كلمة المرور: نجح");
                Console.WriteLine($"✅ التحقق من كلمة المرور: {isValid}");

                Console.WriteLine("\n👥 اختبار User Service...");

                try
                {
                    var allUsers = await userService.GetAllUsersAsync();
                    Console.WriteLine($"✅ عدد المستخدمين الموجودين: {allUsers.Count()}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ خطأ في الحصول على المستخدمين: {ex.Message}");
                    Console.WriteLine("سيتم إنشاء Super Admin Ahmed مباشرة...");
                }

                // إنشاء Super Admin Ahmed
                Console.WriteLine("\n🔧 إنشاء Super Admin Ahmed...");

                var createAdminDto = new CreateUserDto
                {
                    Username = "Ahmed",
                    Email = "<EMAIL>",
                    Password = "Ahmed123!",
                    FullName = "Ahmed Super Admin",
                    PhoneNumber = "+966501234567",
                    RoleId = 1,
                    IsActive = true
                };

                try
                {
                    var adminUser = await userService.CreateUserAsync(createAdminDto);
                    Console.WriteLine($"✅ تم إنشاء Super Admin Ahmed بنجاح - ID: {adminUser.Id}");

                    // اختبار الحصول على المستخدم
                    var testUser = await userService.GetUserByUsernameAsync("Ahmed");
                    if (testUser != null)
                    {
                        Console.WriteLine($"✅ تم العثور على المستخدم");
                        Console.WriteLine($"   👤 المستخدم: {testUser.Username}");
                        Console.WriteLine($"   📧 البريد: {testUser.Email}");
                        Console.WriteLine($"   🎭 الدور: {testUser.RoleName}");
                        Console.WriteLine($"   🔓 عدد الصلاحيات: {testUser.Permissions?.Count ?? 0}");
                        Console.WriteLine($"   ✅ نشط: {testUser.IsActive}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ فشل في إنشاء Super Admin: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"تفاصيل الخطأ: {ex.InnerException.Message}");
                    }
                }



                Console.WriteLine("\n📊 اختبار البيانات الأخرى...");

                // اختبار الأصناف
                var categories = await dbContext.Categories.CountAsync();
                Console.WriteLine($"✅ عدد الأصناف: {categories}");

                // اختبار المنتجات
                var products = await dbContext.Products.CountAsync();
                Console.WriteLine($"✅ عدد المنتجات: {products}");

                // اختبار الموردين
                var suppliers = await dbContext.Suppliers.CountAsync();
                Console.WriteLine($"✅ عدد الموردين: {suppliers}");

                Console.WriteLine("\n🎉 النظام يعمل بشكل صحيح!");
                Console.WriteLine("========================");
                Console.WriteLine("✅ قاعدة البيانات: متصلة");
                Console.WriteLine("✅ Password Service: يعمل");
                Console.WriteLine("✅ User Service: يعمل");
                Console.WriteLine("✅ Super Admin Ahmed: جاهز");

                Console.WriteLine("\n🔐 بيانات تسجيل الدخول:");
                Console.WriteLine("Username: Ahmed");
                Console.WriteLine("Password: Ahmed123!");
                Console.WriteLine("Email: <EMAIL>");
                Console.WriteLine("Role: Admin");
            }
            else
            {
                Console.WriteLine("❌ فشل الاتصال بقاعدة البيانات");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
        }

        Console.WriteLine("\nاضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
}
