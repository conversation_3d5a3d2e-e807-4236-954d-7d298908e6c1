# 🧪 اختبار شامل لـ Electronics Store API مع JWT

Write-Host "🧪 اختبار شامل لـ Electronics Store API مع JWT" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

$baseUrl = "http://localhost:5000"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

# متغيرات عامة
$token = ""
$refreshToken = ""

function Test-APIEndpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = "",
        [string]$Description = ""
    )
    
    Write-Host "`n🔍 $Description" -ForegroundColor Yellow
    Write-Host "   $Method $Url" -ForegroundColor Gray
    
    try {
        $response = if ($Body -eq "") {
            Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers
        } else {
            Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -Body $Body
        }
        
        Write-Host "   ✅ نجح" -ForegroundColor Green
        return $response
    }
    catch {
        Write-Host "   ❌ فشل: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 1. اختبار الاتصال الأساسي
Write-Host "`n📡 اختبار الاتصال الأساسي" -ForegroundColor Magenta
Write-Host "=========================" -ForegroundColor Magenta

$testResponse = Test-APIEndpoint -Url "$baseUrl/api/test" -Description "اختبار الاتصال الأساسي"
if ($testResponse) {
    Write-Host "   📊 الاستجابة: $($testResponse.message)" -ForegroundColor Green
}

$dbTest = Test-APIEndpoint -Url "$baseUrl/api/test/database" -Description "اختبار الاتصال بقاعدة البيانات"
if ($dbTest) {
    Write-Host "   📊 حالة قاعدة البيانات: $($dbTest.message)" -ForegroundColor Green
}

# 2. إنشاء Super Admin Ahmed
Write-Host "`n👤 إنشاء Super Admin Ahmed" -ForegroundColor Magenta
Write-Host "===========================" -ForegroundColor Magenta

$createUserBody = @{
    username = "Ahmed"
    email = "<EMAIL>"
    password = "Ahmed123!"
    fullName = "Ahmed Super Admin"
    phoneNumber = "+966501234567"
    roleId = 1
    isActive = $true
} | ConvertTo-Json

$newUser = Test-APIEndpoint -Url "$baseUrl/api/auth/register" -Method "POST" -Headers $headers -Body $createUserBody -Description "إنشاء Super Admin Ahmed"

if ($newUser) {
    Write-Host "   👤 تم إنشاء المستخدم: $($newUser.user.username)" -ForegroundColor Green
    Write-Host "   📧 البريد الإلكتروني: $($newUser.user.email)" -ForegroundColor Green
    Write-Host "   🎭 الدور: $($newUser.user.roleName)" -ForegroundColor Green
}

# 3. تسجيل الدخول والحصول على JWT Token
Write-Host "`n🔐 تسجيل الدخول والحصول على JWT Token" -ForegroundColor Magenta
Write-Host "=======================================" -ForegroundColor Magenta

$loginBody = @{
    username = "Ahmed"
    password = "Ahmed123!"
} | ConvertTo-Json

$loginResponse = Test-APIEndpoint -Url "$baseUrl/api/auth/login" -Method "POST" -Headers $headers -Body $loginBody -Description "تسجيل الدخول"

if ($loginResponse) {
    $token = $loginResponse.token
    $refreshToken = $loginResponse.refreshToken
    
    Write-Host "   👤 المستخدم: $($loginResponse.user.username)" -ForegroundColor Green
    Write-Host "   🎭 الدور: $($loginResponse.user.roleName)" -ForegroundColor Green
    Write-Host "   🔑 Token: $($token.Substring(0, 50))..." -ForegroundColor Green
    Write-Host "   🔄 Refresh Token: $($refreshToken.Substring(0, 20))..." -ForegroundColor Green
    Write-Host "   ⏰ انتهاء الصلاحية: $($loginResponse.expiresAt)" -ForegroundColor Green
    
    # إضافة Token إلى Headers
    $headers["Authorization"] = "Bearer $token"
}

# 4. اختبار Token Validation
Write-Host "`n🔍 اختبار Token Validation" -ForegroundColor Magenta
Write-Host "===========================" -ForegroundColor Magenta

if ($token -ne "") {
    $validateBody = "`"$token`""
    $validateResponse = Test-APIEndpoint -Url "$baseUrl/api/auth/validate-token" -Method "POST" -Headers $headers -Body $validateBody -Description "التحقق من صحة Token"
    
    if ($validateResponse) {
        Write-Host "   ✅ صحة Token: $($validateResponse.isValid)" -ForegroundColor Green
        Write-Host "   ⏰ منتهي الصلاحية: $($validateResponse.isExpired)" -ForegroundColor Green
        Write-Host "   📅 تاريخ انتهاء الصلاحية: $($validateResponse.expirationDate)" -ForegroundColor Green
    }
}

# 5. اختبار الحصول على معلومات المستخدم الحالي
Write-Host "`n👤 اختبار الحصول على معلومات المستخدم الحالي" -ForegroundColor Magenta
Write-Host "=============================================" -ForegroundColor Magenta

if ($token -ne "") {
    $currentUser = Test-APIEndpoint -Url "$baseUrl/api/auth/me" -Headers $headers -Description "الحصول على معلومات المستخدم الحالي"
    
    if ($currentUser) {
        Write-Host "   👤 المستخدم: $($currentUser.user.username)" -ForegroundColor Green
        Write-Host "   📧 البريد: $($currentUser.user.email)" -ForegroundColor Green
        Write-Host "   🎭 الدور: $($currentUser.user.roleName)" -ForegroundColor Green
        Write-Host "   🔓 الصلاحيات: $($currentUser.user.permissions.Count)" -ForegroundColor Green
        Write-Host "   ✅ نشط: $($currentUser.user.isActive)" -ForegroundColor Green
    }
}

# 6. اختبار APIs المحمية
Write-Host "`n🛡️ اختبار APIs المحمية" -ForegroundColor Magenta
Write-Host "======================" -ForegroundColor Magenta

if ($token -ne "") {
    # اختبار Users API
    $users = Test-APIEndpoint -Url "$baseUrl/api/users" -Headers $headers -Description "الحصول على قائمة المستخدمين"
    if ($users) {
        Write-Host "   👥 عدد المستخدمين: $($users.Count)" -ForegroundColor Green
    }
    
    # اختبار Expenses API
    $expenses = Test-APIEndpoint -Url "$baseUrl/api/expenses" -Headers $headers -Description "الحصول على قائمة المصروفات"
    if ($expenses) {
        Write-Host "   💰 عدد المصروفات: $($expenses.Count)" -ForegroundColor Green
    }
    
    # إنشاء مصروف جديد
    $expenseBody = @{
        expenseType = "اختبار JWT"
        amount = 150.75
        note = "مصروف تجريبي لاختبار JWT Authentication"
    } | ConvertTo-Json
    
    $newExpense = Test-APIEndpoint -Url "$baseUrl/api/expenses" -Method "POST" -Headers $headers -Body $expenseBody -Description "إنشاء مصروف جديد"
    if ($newExpense) {
        Write-Host "   💰 تم إنشاء مصروف: $($newExpense.expenseType)" -ForegroundColor Green
        Write-Host "   💵 المبلغ: $($newExpense.amount)" -ForegroundColor Green
    }
}

# 7. اختبار APIs غير المحمية (يجب أن تعمل بدون Token)
Write-Host "`n🌐 اختبار APIs غير المحمية" -ForegroundColor Magenta
Write-Host "=========================" -ForegroundColor Magenta

# إزالة Authorization header مؤقتاً
$tempHeaders = $headers.Clone()
$tempHeaders.Remove("Authorization")

$categories = Test-APIEndpoint -Url "$baseUrl/api/categories" -Headers $tempHeaders -Description "الحصول على الأصناف (بدون token)"
if ($categories) {
    Write-Host "   📦 عدد الأصناف: $($categories.Count)" -ForegroundColor Green
}

$products = Test-APIEndpoint -Url "$baseUrl/api/products" -Headers $tempHeaders -Description "الحصول على المنتجات (بدون token)"
if ($products) {
    Write-Host "   🛍️ عدد المنتجات: $($products.Count)" -ForegroundColor Green
}

# 8. اختبار تسجيل الخروج
Write-Host "`n🚪 اختبار تسجيل الخروج" -ForegroundColor Magenta
Write-Host "=====================" -ForegroundColor Magenta

if ($token -ne "") {
    $logoutResponse = Test-APIEndpoint -Url "$baseUrl/api/auth/logout" -Method "POST" -Headers $headers -Description "تسجيل الخروج"
    
    if ($logoutResponse) {
        Write-Host "   👤 المستخدم: $($logoutResponse.username)" -ForegroundColor Green
        Write-Host "   ⏰ وقت الخروج: $($logoutResponse.logoutTime)" -ForegroundColor Green
    }
}

# 9. ملخص النتائج
Write-Host "`n📊 ملخص نتائج الاختبار" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

Write-Host "✅ الاتصال الأساسي: تم" -ForegroundColor Green
Write-Host "✅ قاعدة البيانات: متصلة" -ForegroundColor Green
Write-Host "✅ إنشاء Super Admin: تم" -ForegroundColor Green
Write-Host "✅ JWT Authentication: يعمل" -ForegroundColor Green
Write-Host "✅ Token Validation: يعمل" -ForegroundColor Green
Write-Host "✅ Protected APIs: محمية" -ForegroundColor Green
Write-Host "✅ Public APIs: متاحة" -ForegroundColor Green
Write-Host "✅ Logout: يعمل" -ForegroundColor Green

Write-Host "`n🎉 النظام جاهز للاستخدام!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

Write-Host "`n🔐 بيانات تسجيل الدخول:" -ForegroundColor Yellow
Write-Host "Username: Ahmed" -ForegroundColor White
Write-Host "Password: Ahmed123!" -ForegroundColor White
Write-Host "Email: <EMAIL>" -ForegroundColor White
Write-Host "Role: Admin" -ForegroundColor White
