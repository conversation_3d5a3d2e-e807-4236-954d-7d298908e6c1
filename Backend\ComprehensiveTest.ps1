# 🧪 اختبار شامل لجميع العمليات في النظام
Write-Host "🧪 اختبار شامل لجميع العمليات في النظام" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

$baseUrl = "http://localhost:5000"
$token = ""
$headers = @{}

# دالة لاختبار API
function Test-API {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = "",
        [string]$Description = ""
    )
    
    Write-Host "`n🔍 $Description" -ForegroundColor Yellow
    Write-Host "   $Method $Url" -ForegroundColor Gray
    
    try {
        if ($Body -eq "") {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -Body $Body -ContentType "application/json"
        }
        
        Write-Host "   ✅ نجح" -ForegroundColor Green
        return $response
    }
    catch {
        Write-Host "   ❌ فشل: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 1. اختبار الاتصال الأساسي
Write-Host "`n📡 المرحلة 1: اختبار الاتصال الأساسي" -ForegroundColor Magenta
Write-Host "=======================================" -ForegroundColor Magenta

$testResult = Test-API -Url "$baseUrl/api/test" -Description "اختبار الاتصال الأساسي"
if ($testResult) {
    Write-Host "   📊 الاستجابة: $($testResult.message)" -ForegroundColor Green
}

$dbTest = Test-API -Url "$baseUrl/api/test/database" -Description "اختبار الاتصال بقاعدة البيانات"
if ($dbTest) {
    Write-Host "   📊 حالة قاعدة البيانات: $($dbTest.message)" -ForegroundColor Green
}

# 2. تسجيل الدخول والحصول على JWT Token
Write-Host "`n🔐 المرحلة 2: تسجيل الدخول والحصول على JWT Token" -ForegroundColor Magenta
Write-Host "=================================================" -ForegroundColor Magenta

$loginBody = '{"username":"Ahmed","password":"Ahmed123!"}'
$loginResult = Test-API -Url "$baseUrl/api/auth/login" -Method "POST" -Body $loginBody -Description "تسجيل الدخول"

if ($loginResult) {
    $token = $loginResult.token
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    Write-Host "   👤 المستخدم: $($loginResult.user.username)" -ForegroundColor Green
    Write-Host "   🎭 الدور: $($loginResult.user.roleName)" -ForegroundColor Green
    Write-Host "   🔑 Token: $($token.Substring(0,50))..." -ForegroundColor Green
    Write-Host "   ⏰ انتهاء الصلاحية: $($loginResult.expiresAt)" -ForegroundColor Green
} else {
    Write-Host "❌ فشل تسجيل الدخول - توقف الاختبار" -ForegroundColor Red
    exit
}

# 3. اختبار Authentication APIs
Write-Host "`n🔑 المرحلة 3: اختبار Authentication APIs" -ForegroundColor Magenta
Write-Host "=======================================" -ForegroundColor Magenta

$currentUser = Test-API -Url "$baseUrl/api/auth/me" -Headers $headers -Description "الحصول على معلومات المستخدم الحالي"
if ($currentUser) {
    Write-Host "   👤 المستخدم: $($currentUser.user.username)" -ForegroundColor Green
    Write-Host "   📧 البريد: $($currentUser.user.email)" -ForegroundColor Green
    Write-Host "   🎭 الدور: $($currentUser.user.roleName)" -ForegroundColor Green
    Write-Host "   🔓 الصلاحيات: $($currentUser.user.permissions.Count)" -ForegroundColor Green
}

$validateBody = "`"$token`""
$validateResult = Test-API -Url "$baseUrl/api/auth/validate-token" -Method "POST" -Headers $headers -Body $validateBody -Description "التحقق من صحة Token"
if ($validateResult) {
    Write-Host "   ✅ صحة Token: $($validateResult.isValid)" -ForegroundColor Green
    Write-Host "   ⏰ منتهي الصلاحية: $($validateResult.isExpired)" -ForegroundColor Green
}

# 4. اختبار Categories APIs
Write-Host "`n📦 المرحلة 4: اختبار Categories APIs" -ForegroundColor Magenta
Write-Host "===================================" -ForegroundColor Magenta

$categories = Test-API -Url "$baseUrl/api/categories" -Description "الحصول على قائمة الأصناف"
if ($categories) {
    Write-Host "   📦 عدد الأصناف: $($categories.Count)" -ForegroundColor Green
}

# إنشاء صنف جديد
$categoryBody = '{"name":"اختبار API","description":"صنف تجريبي لاختبار APIs"}'
$newCategory = Test-API -Url "$baseUrl/api/categories" -Method "POST" -Headers $headers -Body $categoryBody -Description "إنشاء صنف جديد"
if ($newCategory) {
    Write-Host "   📦 تم إنشاء صنف: $($newCategory.name)" -ForegroundColor Green
    $categoryId = $newCategory.id
}

# 5. اختبار Products APIs
Write-Host "`n🛍️ المرحلة 5: اختبار Products APIs" -ForegroundColor Magenta
Write-Host "=================================" -ForegroundColor Magenta

$products = Test-API -Url "$baseUrl/api/products" -Description "الحصول على قائمة المنتجات"
if ($products) {
    Write-Host "   🛍️ عدد المنتجات: $($products.Count)" -ForegroundColor Green
}

# 6. اختبار Suppliers APIs
Write-Host "`n🏪 المرحلة 6: اختبار Suppliers APIs" -ForegroundColor Magenta
Write-Host "=================================" -ForegroundColor Magenta

$suppliers = Test-API -Url "$baseUrl/api/suppliers" -Description "الحصول على قائمة الموردين"
if ($suppliers) {
    Write-Host "   🏪 عدد الموردين: $($suppliers.Count)" -ForegroundColor Green
}

# 7. اختبار Expenses APIs
Write-Host "`n💰 المرحلة 7: اختبار Expenses APIs" -ForegroundColor Magenta
Write-Host "================================" -ForegroundColor Magenta

$expenses = Test-API -Url "$baseUrl/api/expenses" -Headers $headers -Description "الحصول على قائمة المصروفات"
if ($expenses) {
    Write-Host "   💰 عدد المصروفات: $($expenses.Count)" -ForegroundColor Green
}

# إنشاء مصروف جديد
$expenseBody = '{"expenseType":"اختبار شامل","amount":500.75,"note":"مصروف تجريبي للاختبار الشامل"}'
$newExpense = Test-API -Url "$baseUrl/api/expenses" -Method "POST" -Headers $headers -Body $expenseBody -Description "إنشاء مصروف جديد"
if ($newExpense) {
    Write-Host "   💰 تم إنشاء مصروف: $($newExpense.expenseType)" -ForegroundColor Green
    Write-Host "   💵 المبلغ: $($newExpense.amount)" -ForegroundColor Green
}

# 8. اختبار Users APIs
Write-Host "`n👥 المرحلة 8: اختبار Users APIs" -ForegroundColor Magenta
Write-Host "=============================" -ForegroundColor Magenta

$users = Test-API -Url "$baseUrl/api/users" -Headers $headers -Description "الحصول على قائمة المستخدمين"
if ($users) {
    Write-Host "   👥 عدد المستخدمين: $($users.Count)" -ForegroundColor Green
}

# 9. اختبار Dashboard APIs
Write-Host "`n📊 المرحلة 9: اختبار Dashboard APIs" -ForegroundColor Magenta
Write-Host "=================================" -ForegroundColor Magenta

$dashboard = Test-API -Url "$baseUrl/api/dashboard" -Headers $headers -Description "الحصول على بيانات لوحة التحكم"
if ($dashboard) {
    Write-Host "   📊 بيانات لوحة التحكم متوفرة" -ForegroundColor Green
}

# 10. اختبار Sales Invoices APIs
Write-Host "`n🛒 المرحلة 10: اختبار Sales Invoices APIs" -ForegroundColor Magenta
Write-Host "=======================================" -ForegroundColor Magenta

$salesInvoices = Test-API -Url "$baseUrl/api/sales-invoices" -Headers $headers -Description "الحصول على فواتير البيع"
if ($salesInvoices) {
    Write-Host "   🛒 عدد فواتير البيع: $($salesInvoices.Count)" -ForegroundColor Green
}

# 11. اختبار Purchase Invoices APIs
Write-Host "`n📦 المرحلة 11: اختبار Purchase Invoices APIs" -ForegroundColor Magenta
Write-Host "=========================================" -ForegroundColor Magenta

$purchaseInvoices = Test-API -Url "$baseUrl/api/purchase-invoices" -Headers $headers -Description "الحصول على فواتير الشراء"
if ($purchaseInvoices) {
    Write-Host "   📦 عدد فواتير الشراء: $($purchaseInvoices.Count)" -ForegroundColor Green
}

# 12. اختبار Inventory APIs
Write-Host "`n📋 المرحلة 12: اختبار Inventory APIs" -ForegroundColor Magenta
Write-Host "=================================" -ForegroundColor Magenta

$inventory = Test-API -Url "$baseUrl/api/inventory" -Headers $headers -Description "الحصول على بيانات المخزون"
if ($inventory) {
    Write-Host "   📋 عدد عناصر المخزون: $($inventory.Count)" -ForegroundColor Green
}

# 13. اختبار Returns APIs
Write-Host "`n🔄 المرحلة 13: اختبار Returns APIs" -ForegroundColor Magenta
Write-Host "===============================" -ForegroundColor Magenta

$returns = Test-API -Url "$baseUrl/api/returns" -Headers $headers -Description "الحصول على المرتجعات"
if ($returns) {
    Write-Host "   🔄 عدد المرتجعات: $($returns.Count)" -ForegroundColor Green
}

# 14. اختبار تسجيل الخروج
Write-Host "`n🚪 المرحلة 14: اختبار تسجيل الخروج" -ForegroundColor Magenta
Write-Host "===============================" -ForegroundColor Magenta

$logoutResult = Test-API -Url "$baseUrl/api/auth/logout" -Method "POST" -Headers $headers -Description "تسجيل الخروج"
if ($logoutResult) {
    Write-Host "   👤 المستخدم: $($logoutResult.username)" -ForegroundColor Green
    Write-Host "   ⏰ وقت الخروج: $($logoutResult.logoutTime)" -ForegroundColor Green
}

# 15. ملخص النتائج
Write-Host "`n📊 ملخص نتائج الاختبار الشامل" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

Write-Host "✅ الاتصال الأساسي: تم" -ForegroundColor Green
Write-Host "✅ قاعدة البيانات: متصلة" -ForegroundColor Green
Write-Host "✅ JWT Authentication: يعمل" -ForegroundColor Green
Write-Host "✅ Protected APIs: محمية" -ForegroundColor Green
Write-Host "✅ CRUD Operations: تعمل" -ForegroundColor Green
Write-Host "✅ جميع Controllers: تم اختبارها" -ForegroundColor Green

Write-Host "`n🎉 الاختبار الشامل مكتمل!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
