/**
 * Local Sales Storage Manager
 * Handles sales data storage in localStorage until backend is ready
 */

class SalesStorage {
    constructor() {
        this.storageKey = 'pos_sales';
        this.inventoryKey = 'pos_inventory_movements';
        this.init();
    }

    init() {
        // Initialize storage if not exists
        if (!localStorage.getItem(this.storageKey)) {
            localStorage.setItem(this.storageKey, JSON.stringify([]));
        }
        if (!localStorage.getItem(this.inventoryKey)) {
            localStorage.setItem(this.inventoryKey, JSON.stringify([]));
        }
    }

    // Save sale to localStorage
    async saveSale(saleData) {
        try {
            const sales = this.getSales();
            const newSale = {
                id: this.generateId(),
                ...saleData,
                createdAt: new Date().toISOString(),
                status: 'completed'
            };

            sales.push(newSale);
            localStorage.setItem(this.storageKey, JSON.stringify(sales));

            console.log('💾 Sale saved locally:', newSale);
            return newSale;
        } catch (error) {
            console.error('Error saving sale:', error);
            throw error;
        }
    }

    // Get all sales
    getSales() {
        try {
            const sales = localStorage.getItem(this.storageKey);
            return sales ? JSON.parse(sales) : [];
        } catch (error) {
            console.error('Error getting sales:', error);
            return [];
        }
    }

    // Get sale by ID
    getSaleById(id) {
        const sales = this.getSales();
        return sales.find(sale => sale.id == id);
    }

    // Save inventory movement
    async saveInventoryMovement(movementData) {
        try {
            const movements = this.getInventoryMovements();
            const newMovement = {
                id: this.generateId(),
                ...movementData,
                createdAt: new Date().toISOString()
            };

            movements.push(newMovement);
            localStorage.setItem(this.inventoryKey, JSON.stringify(movements));

            console.log('📦 Inventory movement saved:', newMovement);
            return newMovement;
        } catch (error) {
            console.error('Error saving inventory movement:', error);
            throw error;
        }
    }

    // Get inventory movements
    getInventoryMovements(productId = null) {
        try {
            const movements = localStorage.getItem(this.inventoryKey);
            const allMovements = movements ? JSON.parse(movements) : [];
            
            if (productId) {
                return allMovements.filter(movement => movement.productId == productId);
            }
            
            return allMovements;
        } catch (error) {
            console.error('Error getting inventory movements:', error);
            return [];
        }
    }

    // Update product quantity in localStorage
    async updateProductQuantity(productId, newQuantity) {
        try {
            // Update in products storage if exists
            const productsKey = 'pos_products';
            const products = JSON.parse(localStorage.getItem(productsKey) || '[]');
            
            const productIndex = products.findIndex(p => p.id == productId);
            if (productIndex !== -1) {
                products[productIndex].currentQuantity = newQuantity;
                localStorage.setItem(productsKey, JSON.stringify(products));
            }

            // Also update in inventory storage if exists
            const inventoryKey = 'pos_inventory';
            const inventory = JSON.parse(localStorage.getItem(inventoryKey) || '[]');
            
            const inventoryIndex = inventory.findIndex(p => p.id == productId);
            if (inventoryIndex !== -1) {
                inventory[inventoryIndex].currentQuantity = newQuantity;
                localStorage.setItem(inventoryKey, JSON.stringify(inventory));
            }

            console.log(`📊 Updated product ${productId} quantity to ${newQuantity}`);
            return true;
        } catch (error) {
            console.error('Error updating product quantity:', error);
            throw error;
        }
    }

    // Generate unique ID
    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    // Get sales statistics
    getSalesStats(startDate = null, endDate = null) {
        const sales = this.getSales();
        let filteredSales = sales;

        if (startDate || endDate) {
            filteredSales = sales.filter(sale => {
                const saleDate = new Date(sale.createdAt);
                if (startDate && saleDate < new Date(startDate)) return false;
                if (endDate && saleDate > new Date(endDate)) return false;
                return true;
            });
        }

        const totalSales = filteredSales.length;
        const totalRevenue = filteredSales.reduce((sum, sale) => sum + (sale.total || 0), 0);
        const totalItems = filteredSales.reduce((sum, sale) => 
            sum + (sale.items?.reduce((itemSum, item) => itemSum + item.quantity, 0) || 0), 0);

        return {
            totalSales,
            totalRevenue,
            totalItems,
            averageSale: totalSales > 0 ? totalRevenue / totalSales : 0
        };
    }

    // Export sales data (for backup or migration)
    exportSales() {
        return {
            sales: this.getSales(),
            movements: this.getInventoryMovements(),
            exportDate: new Date().toISOString()
        };
    }

    // Import sales data
    importSales(data) {
        try {
            if (data.sales) {
                localStorage.setItem(this.storageKey, JSON.stringify(data.sales));
            }
            if (data.movements) {
                localStorage.setItem(this.inventoryKey, JSON.stringify(data.movements));
            }
            console.log('📥 Sales data imported successfully');
            return true;
        } catch (error) {
            console.error('Error importing sales data:', error);
            return false;
        }
    }

    // Clear all sales data (use with caution)
    clearAllData() {
        localStorage.removeItem(this.storageKey);
        localStorage.removeItem(this.inventoryKey);
        this.init();
        console.log('🗑️ All sales data cleared');
    }
}

// Create global instance
window.salesStorage = new SalesStorage();
