@echo off
echo ========================================
echo    نظام إدارة المشتريات والمخزون
echo ========================================
echo.

echo تشغيل Backend (ASP.NET Core)...
start "Backend API" cmd /k "cd Backend && dotnet restore && dotnet run --project ElectronicsStore.WebAPI"

echo انتظار تشغيل Backend...
timeout /t 5 /nobreak > nul

echo فتح Frontend (HTML)...
start "" "Frontend/index.html"

echo.
echo تم تشغيل النظام بنجاح!
echo Backend API: https://localhost:7000 او http://localhost:5001
echo Swagger UI: https://localhost:7000/swagger
echo Frontend: تم فتحه في المتصفح
echo.
echo ملاحظة:
echo - Backend يعمل بـ ASP.NET Core مع Clean Architecture
echo - Frontend يعمل بـ HTML/CSS/JavaScript
echo - لاستخدام APIs تحتاج لإنشاء قاعدة البيانات أولاً
echo.
pause
