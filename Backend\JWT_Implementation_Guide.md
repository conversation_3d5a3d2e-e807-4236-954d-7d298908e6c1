# 🔑 JWT Implementation Guide

## 📋 Overview
This guide explains the JWT (JSON Web Token) implementation in the Electronics Store API.

## 🚀 Features Implemented

### ✅ JWT Token Generation
- **Access Token**: 60 minutes expiration
- **Refresh Token**: 7 days expiration
- **User Claims**: ID, Username, Email, Role, Permissions
- **Secure Signing**: HMAC SHA256

### ✅ JWT Token Validation
- **Token Validation**: Signature, expiration, issuer, audience
- **Claims Extraction**: User information from token
- **Permission Checking**: Role-based access control

### ✅ Authentication Endpoints
```http
POST /api/auth/login           # Login with JWT token
POST /api/auth/logout          # Logout (with token info)
POST /api/auth/refresh-token   # Refresh expired token
POST /api/auth/validate-token  # Validate token
GET  /api/auth/me             # Get current user info
```

## 🔧 Configuration

### JWT Settings (appsettings.json)
```json
{
  "JwtSettings": {
    "SecretKey": "ElectronicsStore_SuperSecretKey_2024_MustBe32CharactersOrMore_ForSecurity",
    "Issuer": "ElectronicsStore.API",
    "Audience": "ElectronicsStore.Client",
    "ExpirationInMinutes": 60,
    "RefreshTokenExpirationInDays": 7,
    "ValidateIssuer": true,
    "ValidateAudience": true,
    "ValidateLifetime": true,
    "ValidateIssuerSigningKey": true,
    "ClockSkewInSeconds": 0
  }
}
```

## 🔐 Usage Examples

### 1. Login Request
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password123"
}
```

### 2. Login Response
```json
{
  "message": "تم تسجيل الدخول بنجاح",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "fullName": "Administrator",
    "roleName": "Admin",
    "permissions": ["CREATE_USER", "DELETE_USER", "VIEW_REPORTS"]
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "base64RefreshToken...",
  "expiresAt": "2024-01-01T13:00:00Z"
}
```

### 3. Authenticated Request
```http
GET /api/expenses
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 4. Get Current User
```http
GET /api/auth/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🛡️ Security Features

### Token Claims Structure
```json
{
  "nameid": "1",                    // User ID
  "unique_name": "admin",           // Username
  "email": "<EMAIL>",     // Email
  "role": "Admin",                  // Role Name
  "FullName": "Administrator",      // Full Name
  "IsActive": "true",               // Active Status
  "CreatedAt": "2024-01-01 10:00:00", // Creation Date
  "Permission": ["CREATE_USER", "DELETE_USER"], // Permissions Array
  "iss": "ElectronicsStore.API",    // Issuer
  "aud": "ElectronicsStore.Client", // Audience
  "exp": 1704110400,                // Expiration
  "iat": 1704106800                 // Issued At
}
```

### Helper Extensions
```csharp
// In Controllers, you can use:
var userId = User.GetCurrentUserId();
var username = User.GetCurrentUsername();
var role = User.GetCurrentUserRole();
var permissions = User.GetCurrentUserPermissions();
bool hasPermission = User.HasPermission("CREATE_USER");
bool isAdmin = User.IsInRole("Admin");
```

## 🔄 Token Lifecycle

### 1. Token Generation (Login)
```
User Login → Validate Credentials → Generate JWT → Return Token
```

### 2. Token Usage (API Calls)
```
API Request → Extract Token → Validate Token → Extract Claims → Process Request
```

### 3. Token Refresh
```
Expired Token → Send Refresh Token → Validate Refresh → Generate New JWT
```

### 4. Token Logout
```
Logout Request → Log Token Info → Return Success
```

## 🚨 Error Handling

### Common JWT Errors
- **401 Unauthorized**: Invalid or missing token
- **403 Forbidden**: Valid token but insufficient permissions
- **Token Expired**: Token has expired, use refresh token

### Error Response Format
```json
{
  "message": "غير مصرح لك بالوصول",
  "error": "Token has expired"
}
```

## 🔧 Protected Controllers

The following controllers are now protected with JWT:
- ✅ **AuthController** (partial - login/register are public)
- ✅ **UsersController** (all endpoints)
- ✅ **ExpensesController** (all endpoints)
- 🔄 **Other controllers** (to be updated)

## 📝 Next Steps

1. **Update remaining controllers** with JWT authorization
2. **Implement refresh token storage** in database
3. **Add token blacklisting** for logout
4. **Implement role-based permissions** on endpoints
5. **Add JWT middleware** for automatic token validation

## 🧪 Testing

### Test JWT with Postman/Swagger
1. Login to get token
2. Copy the token
3. Add to Authorization header: `Bearer {token}`
4. Make authenticated requests

### Validate Token
```http
POST /api/auth/validate-token
Content-Type: application/json

{
  "token": "your-jwt-token-here"
}
```

## 🔒 Security Best Practices

1. **Secret Key**: Use strong, random secret key (32+ characters)
2. **HTTPS**: Always use HTTPS in production
3. **Token Storage**: Store tokens securely on client side
4. **Expiration**: Use short expiration times for access tokens
5. **Refresh Tokens**: Implement secure refresh token mechanism
6. **Blacklisting**: Implement token blacklisting for logout

---

**JWT Implementation Complete! 🎉**
