# 🧪 اختبار بسيط لـ APIs

Write-Host "🧪 اختبار APIs للنظام" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

$baseUrl = "http://localhost:5000"

# 1. اختبار الاتصال الأساسي
Write-Host "`n📡 اختبار الاتصال الأساسي..." -ForegroundColor Yellow
try {
    $testResult = Invoke-RestMethod -Uri "$baseUrl/api/test" -Method GET
    Write-Host "✅ API Test Success: $($testResult.message)" -ForegroundColor Green
} catch {
    Write-Host "❌ API Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# 2. تسجيل الدخول
Write-Host "`n🔐 تسجيل الدخول..." -ForegroundColor Yellow
$loginBody = @{
    username = "<PERSON>"
    password = "Ahmed123!"
} | ConvertTo-<PERSON>son

try {
    $loginResult = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "✅ Login Success!" -ForegroundColor Green
    Write-Host "   👤 User: $($loginResult.user.username)" -ForegroundColor Green
    Write-Host "   🎭 Role: $($loginResult.user.roleName)" -ForegroundColor Green
    Write-Host "   🔑 Token: $($loginResult.token.Substring(0,50))..." -ForegroundColor Green
    
    $token = $loginResult.token
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
} catch {
    Write-Host "❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# 3. اختبار الحصول على معلومات المستخدم الحالي
Write-Host "`n👤 اختبار معلومات المستخدم الحالي..." -ForegroundColor Yellow
try {
    $currentUser = Invoke-RestMethod -Uri "$baseUrl/api/auth/me" -Method GET -Headers $headers
    Write-Host "✅ Current User Success!" -ForegroundColor Green
    Write-Host "   👤 Username: $($currentUser.user.username)" -ForegroundColor Green
    Write-Host "   📧 Email: $($currentUser.user.email)" -ForegroundColor Green
    Write-Host "   🎭 Role: $($currentUser.user.roleName)" -ForegroundColor Green
    Write-Host "   🔓 Permissions: $($currentUser.user.permissions.Count)" -ForegroundColor Green
} catch {
    Write-Host "❌ Current User Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. اختبار إنشاء مصروف
Write-Host "`n💰 اختبار إنشاء مصروف..." -ForegroundColor Yellow
$expenseBody = @{
    expenseType = "اختبار API"
    amount = 250.75
    note = "مصروف تجريبي لاختبار JWT Authentication عبر API"
} | ConvertTo-Json

try {
    $expenseResult = Invoke-RestMethod -Uri "$baseUrl/api/expenses" -Method POST -Body $expenseBody -Headers $headers
    Write-Host "✅ Expense Created!" -ForegroundColor Green
    Write-Host "   💰 Type: $($expenseResult.expenseType)" -ForegroundColor Green
    Write-Host "   💵 Amount: $($expenseResult.amount)" -ForegroundColor Green
    Write-Host "   📝 Note: $($expenseResult.note)" -ForegroundColor Green
} catch {
    Write-Host "❌ Expense Creation Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. اختبار الحصول على قائمة المصروفات
Write-Host "`n📋 اختبار قائمة المصروفات..." -ForegroundColor Yellow
try {
    $expenses = Invoke-RestMethod -Uri "$baseUrl/api/expenses" -Method GET -Headers $headers
    Write-Host "✅ Expenses List Success!" -ForegroundColor Green
    Write-Host "   📊 Total Expenses: $($expenses.Count)" -ForegroundColor Green
} catch {
    Write-Host "❌ Expenses List Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. اختبار الحصول على المنتجات (غير محمي)
Write-Host "`n🛍️ اختبار قائمة المنتجات..." -ForegroundColor Yellow
try {
    $products = Invoke-RestMethod -Uri "$baseUrl/api/products" -Method GET
    Write-Host "✅ Products List Success!" -ForegroundColor Green
    Write-Host "   📦 Total Products: $($products.Count)" -ForegroundColor Green
} catch {
    Write-Host "❌ Products List Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. اختبار تسجيل الخروج
Write-Host "`n🚪 اختبار تسجيل الخروج..." -ForegroundColor Yellow
try {
    $logoutResult = Invoke-RestMethod -Uri "$baseUrl/api/auth/logout" -Method POST -Headers $headers
    Write-Host "✅ Logout Success!" -ForegroundColor Green
    Write-Host "   👤 User: $($logoutResult.username)" -ForegroundColor Green
    Write-Host "   ⏰ Logout Time: $($logoutResult.logoutTime)" -ForegroundColor Green
} catch {
    Write-Host "❌ Logout Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 اختبار APIs مكتمل!" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green
