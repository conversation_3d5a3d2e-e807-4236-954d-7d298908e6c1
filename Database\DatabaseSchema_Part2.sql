-------------------------------------------------------------------------------- 
-- 11. <PERSON><PERSON> للتحقق من الحد الأدنى للسعر 
IF OBJECT_ID(N'dbo.trg_CheckMinSellingPrice','TR') IS NOT NULL 
    DROP TRIGGER dbo.trg_CheckMinSellingPrice; 
GO 
CREATE TRIGGER trg_CheckMinSellingPrice 
ON sales_invoice_details 
AFTER INSERT, UPDATE 
AS 
BEGIN 
    SET NOCOUNT ON; 
    IF EXISTS ( 
        SELECT 1 
        FROM inserted i 
        JOIN products p ON i.product_id = p.id 
        JOIN sales_invoices s ON i.sales_invoice_id = s.id 
        WHERE (i.unit_price - i.discount_amount) < p.min_selling_price 
          AND s.override_by_user_id IS NULL 
    ) 
    BEGIN 
        RAISERROR('سعر البيع أقل من الحد الأدنى بدون تفويض مدير.', 16, 1); 
        ROLLBACK TRANSACTION; 
    END 
END; 
GO 
 
-------------------------------------------------------------------------------- 
-- 12. سجل حركة المخزون (Inventory Logs) 
CREATE TABLE inventory_logs ( 
    id             INT IDENTITY(1,1) PRIMARY KEY, 
    product_id     INT           NOT NULL, 
    movement_type  NVARCHAR(20)  NOT NULL CHECK(movement_type IN('purchase','sale','return_sale','return_purchase','adjust')), 
    quantity       INT           NOT NULL, 
    unit_cost      DECIMAL(10,2) NOT NULL, 
    reference_tbl  NVARCHAR(50)  NOT NULL, 
    reference_id   INT           NOT NULL, 
    note           NVARCHAR(200) NULL, 
    user_id        INT           NOT NULL, 
    created_at     DATETIME      DEFAULT GETDATE(), 
    FOREIGN KEY (product_id) REFERENCES products(id), 
    FOREIGN KEY (user_id)    REFERENCES users(id) 
); 
GO 
 
-------------------------------------------------------------------------------- 
-- 13. مرتجعات المبيعات (Sales Returns) 
CREATE TABLE sales_returns ( 
    id               INT IDENTITY(1,1) PRIMARY KEY, 
    sales_invoice_id INT           NOT NULL, 
    product_id       INT           NOT NULL, 
    quantity         INT           NOT NULL CHECK(quantity > 0), 
    reason           NVARCHAR(200) NULL, 
    user_id          INT           NOT NULL, 
    created_at       DATETIME      DEFAULT GETDATE(), 
    FOREIGN KEY (sales_invoice_id) REFERENCES sales_invoices(id), 
    FOREIGN KEY (product_id)         REFERENCES products(id), 
    FOREIGN KEY (user_id)            REFERENCES users(id) 
); 
GO 
 
-------------------------------------------------------------------------------- 
-- 14. مرتجعات المشتريات (Purchase Returns) 
CREATE TABLE purchase_returns ( 
    id                  INT IDENTITY(1,1) PRIMARY KEY, 
    purchase_invoice_id INT           NOT NULL, 
    product_id          INT           NOT NULL, 
    quantity            INT           NOT NULL CHECK(quantity > 0), 
    reason              NVARCHAR(200) NULL, 
    user_id             INT           NOT NULL, 
    created_at          DATETIME      DEFAULT GETDATE(), 
    FOREIGN KEY (purchase_invoice_id) REFERENCES purchase_invoices(id), 
    FOREIGN KEY (product_id)            REFERENCES products(id), 
    FOREIGN KEY (user_id)               REFERENCES users(id) 
); 
GO 
 
-------------------------------------------------------------------------------- 
-- 15. المصروفات (Expenses) 
CREATE TABLE expenses ( 
    id           INT IDENTITY(1,1) PRIMARY KEY, 
    expense_type NVARCHAR(100) NOT NULL, 
    amount       DECIMAL(12,2)  NOT NULL CHECK(amount > 0), 
    note         NVARCHAR(200)  NULL, 
    user_id      INT            NOT NULL, 
    created_at   DATETIME       DEFAULT GETDATE(), 
    FOREIGN KEY (user_id) REFERENCES users(id) 
); 
GO
