// ===== API Configuration =====
const API_CONFIG = {
    BASE_URLS: [
        'https://localhost:7001/api', // ASP.NET HTTPS
        'http://localhost:5001/api',  // ASP.NET HTTP
        'https://localhost:5001/api', // Alternative HTTPS
        'http://localhost:7001/api'   // Alternative HTTP
    ],
    TIMEOUT: 10000, // 10 seconds
    HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
};

// Current working API URL
let CURRENT_API_URL = null;

// ===== API Helper Functions =====
class ApiService {
    constructor() {
        this.baseUrls = API_CONFIG.BASE_URLS;
        this.timeout = API_CONFIG.TIMEOUT;
        this.currentBaseUrl = CURRENT_API_URL || this.baseUrls[0];
    }

    // Find working API URL
    async findWorkingUrl() {
        if (CURRENT_API_URL) {
            return CURRENT_API_URL;
        }

        console.log('🔍 Testing API endpoints...');

        for (const baseUrl of this.baseUrls) {
            try {
                console.log(`🧪 Testing: ${baseUrl}/test`);

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout for testing

                const response = await fetch(`${baseUrl}/test`, {
                    method: 'GET',
                    headers: API_CONFIG.HEADERS,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    console.log(`✅ Found working API: ${baseUrl}`);
                    CURRENT_API_URL = baseUrl;
                    this.currentBaseUrl = baseUrl;
                    return baseUrl;
                }
            } catch (error) {
                console.log(`❌ Failed: ${baseUrl} - ${error.message}`);
                continue;
            }
        }

        throw new ApiError('لا يمكن الاتصال بالخادم. تأكد من تشغيل Backend', 503);
    }

    // Generic API request method
    async request(endpoint, options = {}) {
        // Try to find working URL if not already found
        if (!CURRENT_API_URL) {
            try {
                await this.findWorkingUrl();
            } catch (error) {
                throw error;
            }
        }

        const url = `${this.currentBaseUrl}${endpoint}`;
        const config = {
            method: options.method || 'GET',
            headers: {
                ...API_CONFIG.HEADERS,
                ...options.headers
            },
            ...options
        };

        // Add authorization header if token exists
        const token = localStorage.getItem('authToken');
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }

        try {
            console.log(`🌐 API Request: ${config.method} ${url}`);

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);

            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            console.log(`📡 API Response: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || `HTTP ${response.status}: ${response.statusText}`,
                    response.status,
                    errorData
                );
            }

            // Handle No Content responses (like DELETE operations)
            if (response.status === 204 || response.headers.get('content-length') === '0') {
                console.log('✅ API Success: No Content');
                return null;
            }

            // Check if response has content
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const data = await response.json();
                console.log('✅ API Success:', data);
                return data;
            } else {
                // For non-JSON responses
                const text = await response.text();
                console.log('✅ API Success (text):', text);
                return { message: text || 'Success' };
            }

        } catch (error) {
            if (error.name === 'AbortError') {
                console.error('⏰ API Timeout:', url);
                throw new ApiError('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.', 408);
            }

            // If connection failed, reset current URL to try others next time
            if (error.message.includes('Failed to fetch') || error.message.includes('CONNECTION_REFUSED')) {
                console.log('🔄 Connection failed, will try other URLs next time');
                CURRENT_API_URL = null;
                this.currentBaseUrl = this.baseUrls[0];
            }

            console.error('❌ API Error:', error);
            throw error;
        }
    }

    // GET request
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { method: 'GET' });
    }

    // POST request
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT request
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE request
    async delete(endpoint) {
        const result = await this.request(endpoint, { method: 'DELETE' });
        // DELETE operations typically return null for 204 No Content
        return result || { success: true, message: 'Deleted successfully' };
    }
}

// ===== Custom Error Class =====
class ApiError extends Error {
    constructor(message, status = 500, data = {}) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.data = data;
    }
}

// ===== API Endpoints =====
const API_ENDPOINTS = {
    // System
    TEST: '/test',
    TEST_DATABASE: '/test/database',
    
    // Authentication
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    ME: '/auth/me',
    VALIDATE_TOKEN: '/auth/validate-token',
    REFRESH_TOKEN: '/auth/refresh-token',
    
    // Users
    USERS: '/users',
    
    // Categories
    CATEGORIES: '/categories',
    
    // Products
    PRODUCTS: '/products',
    
    // Suppliers
    SUPPLIERS: '/suppliers',
    
    // Expenses
    EXPENSES: '/expenses',
    
    // Dashboard
    DASHBOARD_STATS: '/dashboard/stats',
    DASHBOARD_ACTIVITIES: '/dashboard/recent-activities',
    DASHBOARD_ALERTS: '/dashboard/alerts',
    
    // Sales
    SALES_INVOICES: '/sales-invoices',
    
    // Purchases
    PURCHASE_INVOICES: '/purchase-invoices',
    
    // Inventory
    INVENTORY: '/inventory',
    
    // Returns
    RETURNS: '/returns'
};

// ===== Specific API Functions =====

// System Status Check
async function checkSystemStatus() {
    const api = new ApiService();
    const statusElement = document.getElementById('systemStatus');

    if (statusElement) {
        statusElement.className = 'status-indicator checking';
        statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> فحص الاتصال...';
    }

    try {
        // Test basic connection
        const testResult = await api.get(API_ENDPOINTS.TEST);

        if (statusElement) {
            statusElement.className = 'status-indicator online';
            statusElement.innerHTML = `<i class="fas fa-circle"></i> متصل (${api.currentBaseUrl})`;
        }

        console.log('✅ System Status: Online');
        console.log('📊 Backend:', testResult.message);
        console.log('� API URL:', api.currentBaseUrl);

        // Test database connection (optional)
        try {
            const dbResult = await api.get(API_ENDPOINTS.TEST_DATABASE);
            console.log('�🗃️ Database:', dbResult.message);
        } catch (dbError) {
            console.warn('⚠️ Database test failed:', dbError.message);
        }

        return true;

    } catch (error) {
        console.error('❌ System Status: Offline', error);

        let errorMessage = 'النظام غير متصل';

        if (error.message.includes('لا يمكن الاتصال بالخادم')) {
            errorMessage = 'Backend غير يعمل';
        } else if (error.status === 503) {
            errorMessage = 'الخادم غير متاح';
        }

        if (statusElement) {
            statusElement.className = 'status-indicator offline';
            statusElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${errorMessage}`;
        }

        // Show helpful message in console
        console.log('💡 لحل المشكلة:');
        console.log('1. تأكد من تشغيل ASP.NET Backend');
        console.log('2. شغل الأمر: start-backend.bat');
        console.log('3. أو شغل: cd Backend\\ElectronicsStore.WebAPI && dotnet run');

        return false;
    }
}

// Login API
async function loginUser(credentials) {
    const api = new ApiService();
    
    try {
        const response = await api.post(API_ENDPOINTS.LOGIN, credentials);
        
        // Store authentication data
        if (response.token) {
            localStorage.setItem('authToken', response.token);
            localStorage.setItem('refreshToken', response.refreshToken);
            localStorage.setItem('userData', JSON.stringify(response.user));
            localStorage.setItem('loginTime', new Date().toISOString());
        }
        
        return response;
        
    } catch (error) {
        console.error('❌ Login failed:', error);
        throw error;
    }
}

// Logout API
async function logoutUser() {
    const api = new ApiService();
    
    try {
        await api.post(API_ENDPOINTS.LOGOUT);
    } catch (error) {
        console.warn('⚠️ Logout API failed:', error);
    } finally {
        // Clear local storage regardless of API response
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('userData');
        localStorage.removeItem('loginTime');
        
        console.log('🚪 User logged out');
    }
}

// Get Current User
async function getCurrentUser() {
    const api = new ApiService();
    
    try {
        const response = await api.get(API_ENDPOINTS.ME);
        
        // Update stored user data
        localStorage.setItem('userData', JSON.stringify(response.user));
        
        return response.user;
        
    } catch (error) {
        console.error('❌ Get current user failed:', error);
        throw error;
    }
}

// Validate Token
async function validateToken(token) {
    const api = new ApiService();
    
    try {
        const response = await api.post(API_ENDPOINTS.VALIDATE_TOKEN, token);
        return response.isValid && !response.isExpired;
        
    } catch (error) {
        console.error('❌ Token validation failed:', error);
        return false;
    }
}

// ===== Utility Functions =====

// Check if user is authenticated
function isAuthenticated() {
    const token = localStorage.getItem('authToken');
    const userData = localStorage.getItem('userData');
    return !!(token && userData);
}

// Get stored user data
function getStoredUserData() {
    const userData = localStorage.getItem('userData');
    return userData ? JSON.parse(userData) : null;
}

// Get stored auth token
function getAuthToken() {
    return localStorage.getItem('authToken');
}

// Format API error message
function formatApiError(error) {
    if (error instanceof ApiError) {
        return error.message;
    }
    
    if (error.message) {
        return error.message;
    }
    
    return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
}

// Export for use in other files
window.ApiService = ApiService;
window.ApiError = ApiError;
window.API_ENDPOINTS = API_ENDPOINTS;
