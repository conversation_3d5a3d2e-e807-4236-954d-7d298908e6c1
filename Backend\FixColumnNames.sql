-- 🔧 إصلاح أسماء الأعمدة في قاعدة البيانات
-- Electronics Store Database Column Names Fix

USE ElectronicsStoreDB;
GO

PRINT '🔧 بدء إصلاح أسماء الأعمدة...';

-- التحقق من وجود الجداول والأعمدة
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'users')
BEGIN
    PRINT '✅ جدول users موجود';
    
    -- إصلاح أعمدة جدول users
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'CreatedAt')
    BEGIN
        PRINT '🔄 تغيير اسم عمود CreatedAt إلى created_at في جدول users...';
        EXEC sp_rename 'users.CreatedAt', 'created_at', 'COLUMN';
        PRINT '✅ تم تغيير CreatedAt إلى created_at';
    END
    ELSE IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'created_at')
    BEGIN
        PRINT '✅ عمود created_at موجود بالفعل في جدول users';
    END
    ELSE
    BEGIN
        PRINT '⚠️ لم يتم العثور على عمود CreatedAt أو created_at في جدول users';
    END

    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'LastLoginAt')
    BEGIN
        PRINT '🔄 تغيير اسم عمود LastLoginAt إلى last_login_at في جدول users...';
        EXEC sp_rename 'users.LastLoginAt', 'last_login_at', 'COLUMN';
        PRINT '✅ تم تغيير LastLoginAt إلى last_login_at';
    END
    ELSE IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'last_login_at')
    BEGIN
        PRINT '✅ عمود last_login_at موجود بالفعل في جدول users';
    END

    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'FullName')
    BEGIN
        PRINT '🔄 تغيير اسم عمود FullName إلى full_name في جدول users...';
        EXEC sp_rename 'users.FullName', 'full_name', 'COLUMN';
        PRINT '✅ تم تغيير FullName إلى full_name';
    END

    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'PhoneNumber')
    BEGIN
        PRINT '🔄 تغيير اسم عمود PhoneNumber إلى phone_number في جدول users...';
        EXEC sp_rename 'users.PhoneNumber', 'phone_number', 'COLUMN';
        PRINT '✅ تم تغيير PhoneNumber إلى phone_number';
    END

    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'RoleId')
    BEGIN
        PRINT '🔄 تغيير اسم عمود RoleId إلى role_id في جدول users...';
        EXEC sp_rename 'users.RoleId', 'role_id', 'COLUMN';
        PRINT '✅ تم تغيير RoleId إلى role_id';
    END

    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'IsActive')
    BEGIN
        PRINT '🔄 تغيير اسم عمود IsActive إلى is_active في جدول users...';
        EXEC sp_rename 'users.IsActive', 'is_active', 'COLUMN';
        PRINT '✅ تم تغيير IsActive إلى is_active';
    END
END
ELSE
BEGIN
    PRINT '❌ جدول users غير موجود';
END

-- إصلاح أعمدة الجداول الأخرى إذا لزم الأمر
PRINT '🔍 فحص الجداول الأخرى...';

-- جدول categories
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'categories')
BEGIN
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'categories' AND COLUMN_NAME = 'Name')
    BEGIN
        PRINT '🔄 تغيير اسم عمود Name إلى name في جدول categories...';
        EXEC sp_rename 'categories.Name', 'name', 'COLUMN';
        PRINT '✅ تم تغيير Name إلى name في categories';
    END
END

-- جدول products
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'products')
BEGIN
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'Name')
    BEGIN
        PRINT '🔄 تغيير اسم عمود Name إلى name في جدول products...';
        EXEC sp_rename 'products.Name', 'name', 'COLUMN';
        PRINT '✅ تم تغيير Name إلى name في products';
    END
    
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'Description')
    BEGIN
        PRINT '🔄 تغيير اسم عمود Description إلى description في جدول products...';
        EXEC sp_rename 'products.Description', 'description', 'COLUMN';
        PRINT '✅ تم تغيير Description إلى description في products';
    END
    
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'CategoryId')
    BEGIN
        PRINT '🔄 تغيير اسم عمود CategoryId إلى category_id في جدول products...';
        EXEC sp_rename 'products.CategoryId', 'category_id', 'COLUMN';
        PRINT '✅ تم تغيير CategoryId إلى category_id في products';
    END
    
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'SupplierId')
    BEGIN
        PRINT '🔄 تغيير اسم عمود SupplierId إلى supplier_id في جدول products...';
        EXEC sp_rename 'products.SupplierId', 'supplier_id', 'COLUMN';
        PRINT '✅ تم تغيير SupplierId إلى supplier_id في products';
    END
    
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'UnitPrice')
    BEGIN
        PRINT '🔄 تغيير اسم عمود UnitPrice إلى unit_price في جدول products...';
        EXEC sp_rename 'products.UnitPrice', 'unit_price', 'COLUMN';
        PRINT '✅ تم تغيير UnitPrice إلى unit_price في products';
    END
    
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'StockQuantity')
    BEGIN
        PRINT '🔄 تغيير اسم عمود StockQuantity إلى stock_quantity في جدول products...';
        EXEC sp_rename 'products.StockQuantity', 'stock_quantity', 'COLUMN';
        PRINT '✅ تم تغيير StockQuantity إلى stock_quantity في products';
    END
    
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'MinStockLevel')
    BEGIN
        PRINT '🔄 تغيير اسم عمود MinStockLevel إلى min_stock_level في جدول products...';
        EXEC sp_rename 'products.MinStockLevel', 'min_stock_level', 'COLUMN';
        PRINT '✅ تم تغيير MinStockLevel إلى min_stock_level في products';
    END
    
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'IsActive')
    BEGIN
        PRINT '🔄 تغيير اسم عمود IsActive إلى is_active في جدول products...';
        EXEC sp_rename 'products.IsActive', 'is_active', 'COLUMN';
        PRINT '✅ تم تغيير IsActive إلى is_active في products';
    END
    
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'CreatedAt')
    BEGIN
        PRINT '🔄 تغيير اسم عمود CreatedAt إلى created_at في جدول products...';
        EXEC sp_rename 'products.CreatedAt', 'created_at', 'COLUMN';
        PRINT '✅ تم تغيير CreatedAt إلى created_at في products';
    END
END

PRINT '✅ تم الانتهاء من إصلاح أسماء الأعمدة';

-- عرض هيكل جدول users بعد الإصلاح
PRINT '📊 هيكل جدول users بعد الإصلاح:';
SELECT 
    COLUMN_NAME as 'اسم العمود',
    DATA_TYPE as 'نوع البيانات',
    IS_NULLABLE as 'يقبل NULL',
    COLUMN_DEFAULT as 'القيمة الافتراضية'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users'
ORDER BY ORDINAL_POSITION;

PRINT '🎉 تم إصلاح قاعدة البيانات بنجاح!';
