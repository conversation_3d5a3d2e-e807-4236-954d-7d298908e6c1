@echo off
echo ========================================
echo    إعداد Frontend - Angular
echo ========================================
echo.

echo الانتقال إلى مجلد Frontend...
cd Frontend

echo تثبيت Angular CLI عالمياً...
npm install -g @angular/cli@latest
if errorlevel 1 (
    echo خطأ في تثبيت Angular CLI
    pause
    exit /b 1
)

echo تثبيت حزم المشروع...
npm install
if errorlevel 1 (
    echo خطأ في تثبيت حزم npm
    pause
    exit /b 1
)

echo.
echo تم إعداد Frontend بنجاح!
echo يمكنك الآن تشغيل المشروع باستخدام:
echo npm start
echo أو
echo ng serve
echo.
pause
