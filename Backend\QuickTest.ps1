# اختبار سريع للـ APIs
Write-Host "🧪 اختبار سريع للـ APIs" -ForegroundColor Cyan

$baseUrl = "http://localhost:5000"

# 1. اختبار الاتصال
Write-Host "📡 اختبار الاتصال..." -ForegroundColor Yellow
try {
    $test = Invoke-RestMethod -Uri "$baseUrl/api/test" -Method GET
    Write-Host "✅ الاتصال ناجح: $($test.message)" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل الاتصال: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# 2. تسجيل الدخول
Write-Host "🔐 تسجيل الدخول..." -ForegroundColor Yellow
$loginData = '{"username":"<PERSON>","password":"Ahmed123!"}'
try {
    $login = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "✅ تسجيل الدخول ناجح!" -ForegroundColor Green
    Write-Host "   المستخدم: $($login.user.username)" -ForegroundColor Green
    Write-Host "   الدور: $($login.user.roleName)" -ForegroundColor Green
    $token = $login.token
} catch {
    Write-Host "❌ فشل تسجيل الدخول: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# 3. اختبار API محمي
Write-Host "🛡️ اختبار API محمي..." -ForegroundColor Yellow
$headers = @{ "Authorization" = "Bearer $token" }
try {
    $me = Invoke-RestMethod -Uri "$baseUrl/api/auth/me" -Method GET -Headers $headers
    Write-Host "✅ API محمي يعمل!" -ForegroundColor Green
    Write-Host "   المستخدم: $($me.user.username)" -ForegroundColor Green
    Write-Host "   الصلاحيات: $($me.user.permissions.Count)" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل API محمي: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "🎉 الاختبار مكتمل!" -ForegroundColor Green
