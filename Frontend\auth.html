<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول والتسجيل - نظام إدارة المتجر الإلكتروني</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/auth-styles.css">
</head>
<body>
    <div class="auth-container">
        <!-- Sidebar -->
        <div class="auth-sidebar">
            <div>
                <i class="fas fa-store" style="font-size: 3rem; margin-bottom: 20px;"></i>
                <h1>ElectroHub Pro</h1>
                <p>نظام إدارة المتجر الإلكتروني الشامل</p>
                
                <div class="features">
                    <div class="feature">
                        <i class="fas fa-chart-line"></i>
                        <span>تتبع المبيعات والأرباح</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-boxes"></i>
                        <span>إدارة المخزون الذكية</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-users"></i>
                        <span>إدارة العملاء والموردين</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-receipt"></i>
                        <span>نظام فواتير متقدم</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-shield-alt"></i>
                        <span>أمان وحماية عالية</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="auth-main">
            <!-- Tabs -->
            <div class="auth-tabs">
                <div class="auth-tab active" onclick="switchTab('login')">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </div>
                <div class="auth-tab" onclick="switchTab('register')">
                    <i class="fas fa-user-plus"></i>
                    إنشاء حساب
                </div>
            </div>

            <!-- Messages -->
            <div id="successMessage" class="message success">
                <i class="fas fa-check-circle"></i>
                <span id="successText"></span>
            </div>
            <div id="errorMessage" class="message error">
                <i class="fas fa-exclamation-circle"></i>
                <span id="errorText"></span>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="auth-form active">
                <div class="form-group">
                    <label for="loginUsername">
                        <i class="fas fa-user"></i>
                        اسم المستخدم أو البريد الإلكتروني
                    </label>
                    <input 
                        type="text" 
                        id="loginUsername" 
                        name="username" 
                        required 
                        placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                        autocomplete="username"
                    >
                </div>

                <div class="form-group">
                    <label for="loginPassword">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="input-wrapper">
                        <input 
                            type="password" 
                            id="loginPassword" 
                            name="password" 
                            required 
                            placeholder="أدخل كلمة المرور"
                            autocomplete="current-password"
                        >
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('loginPassword')"></i>
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 30px;">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="rememberMe" style="margin-left: 10px;">
                        <span>تذكرني</span>
                    </label>
                </div>

                <button type="submit" class="auth-btn" id="loginBtn">
                    <i class="fas fa-spinner loading-spinner" id="loginSpinner"></i>
                    <span>تسجيل الدخول</span>
                </button>

                <!-- Demo Credentials Button -->
                <button type="button" class="auth-btn" onclick="fillDemoCredentials()" 
                        style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); margin-top: 10px;">
                    <i class="fas fa-flask"></i>
                    بيانات تجريبية
                </button>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="auth-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="registerUsername">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input 
                            type="text" 
                            id="registerUsername" 
                            name="username" 
                            required 
                            placeholder="اختر اسم مستخدم"
                            autocomplete="username"
                        >
                    </div>
                    <div class="form-group">
                        <label for="registerEmail">
                            <i class="fas fa-envelope"></i>
                            البريد الإلكتروني
                        </label>
                        <input 
                            type="email" 
                            id="registerEmail" 
                            name="email" 
                            required 
                            placeholder="أدخل البريد الإلكتروني"
                            autocomplete="email"
                        >
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="registerFullName">
                            <i class="fas fa-id-card"></i>
                            الاسم الكامل
                        </label>
                        <input 
                            type="text" 
                            id="registerFullName" 
                            name="fullName" 
                            required 
                            placeholder="أدخل الاسم الكامل"
                            autocomplete="name"
                        >
                    </div>
                    <div class="form-group">
                        <label for="registerPhone">
                            <i class="fas fa-phone"></i>
                            رقم الهاتف
                        </label>
                        <input 
                            type="tel" 
                            id="registerPhone" 
                            name="phoneNumber" 
                            placeholder="أدخل رقم الهاتف (اختياري)"
                            autocomplete="tel"
                        >
                    </div>
                </div>

                <div class="form-group">
                    <label for="registerPassword">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="input-wrapper">
                        <input 
                            type="password" 
                            id="registerPassword" 
                            name="password" 
                            required 
                            placeholder="أدخل كلمة مرور قوية"
                            autocomplete="new-password"
                            oninput="checkPasswordStrength(this.value)"
                        >
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('registerPassword')"></i>
                    </div>
                    <div class="password-strength" id="passwordStrength">
                        <div class="strength-bar">
                            <div class="strength-fill" id="strengthFill"></div>
                        </div>
                        <span id="strengthText">أدخل كلمة مرور</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">
                        <i class="fas fa-lock"></i>
                        تأكيد كلمة المرور
                    </label>
                    <div class="input-wrapper">
                        <input 
                            type="password" 
                            id="confirmPassword" 
                            name="confirmPassword" 
                            required 
                            placeholder="أعد إدخال كلمة المرور"
                            autocomplete="new-password"
                        >
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('confirmPassword')"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="registerRole">
                        <i class="fas fa-user-tag"></i>
                        الدور
                    </label>
                    <select id="registerRole" name="roleId" required>
                        <option value="">اختر الدور</option>
                        <option value="2">موظف</option>
                        <option value="3">كاشير</option>
                    </select>
                </div>

                <button type="submit" class="auth-btn" id="registerBtn">
                    <i class="fas fa-spinner loading-spinner" id="registerSpinner"></i>
                    <span>إنشاء حساب</span>
                </button>
            </form>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        // Additional page-specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize authentication forms
            initializeAuth();
        });

        // Fill demo credentials
        function fillDemoCredentials() {
            document.getElementById('loginUsername').value = 'Ahmed';
            document.getElementById('loginPassword').value = 'Ahmed123!';
            showMessage('success', 'تم تعبئة البيانات التجريبية');
        }
    </script>
</body>
</html>
