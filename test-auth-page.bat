@echo off
echo ========================================
echo    اختبار صفحة تسجيل الدخول والتسجيل
echo ========================================
echo.

echo 🚀 بدء تشغيل Backend...
cd Backend
start "Backend Server" cmd /k "dotnet run --project ElectronicsStore.WebAPI"

echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak > nul

echo 🌐 فتح صفحة تسجيل الدخول...
cd ..\Frontend
start "" "auth.html"

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo.
echo 📋 بيانات تجريبية:
echo    اسم المستخدم: Ahmed
echo    كلمة المرور: Ahmed123!
echo.
echo 🔗 الروابط:
echo    Backend API: https://localhost:7001
echo    Login Endpoint: https://localhost:7001/api/auth/login12
echo    Frontend: file:///%CD%/login.html
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
