﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Infrastructure.Data;
using ElectronicsStore.Infrastructure.Repositories;
using ElectronicsStore.Domain.Interfaces;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Domain.Entities;

class CreateAdminProgram
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🔧 إنشاء Super Admin Ahmed");
        Console.WriteLine("========================");

        try
        {
            // إعداد الخدمات
            var services = new ServiceCollection();

            // إضافة DbContext
            var connectionString = "Server=localhost;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;";
            services.AddDbContext<ElectronicsStoreDbContext>(options =>
                options.UseSqlServer(connectionString));

            // إضافة الخدمات
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IPasswordService, PasswordService>();

            var serviceProvider = services.BuildServiceProvider();

            using var scope = serviceProvider.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            var passwordService = scope.ServiceProvider.GetRequiredService<IPasswordService>();

            Console.WriteLine("🔍 اختبار الاتصال بقاعدة البيانات...");
            var dbContext = scope.ServiceProvider.GetRequiredService<ElectronicsStoreDbContext>();
            var canConnect = await dbContext.Database.CanConnectAsync();

            if (!canConnect)
            {
                Console.WriteLine("❌ فشل الاتصال بقاعدة البيانات");
                return;
            }

            Console.WriteLine("✅ الاتصال بقاعدة البيانات ناجح");

            // تشفير كلمة المرور
            Console.WriteLine("\n🔐 تشفير كلمة المرور...");
            var password = "Ahmed123!";
            var hashedPassword = passwordService.HashPassword(password);
            Console.WriteLine($"✅ تم تشفير كلمة المرور: {hashedPassword.Substring(0, 20)}...");

            // إنشاء المستخدم
            Console.WriteLine("\n👤 إنشاء Super Admin Ahmed...");
            var user = new User
            {
                Username = "Ahmed",
                Email = "<EMAIL>",
                Password = hashedPassword,
                FullName = "Ahmed Super Admin",
                PhoneNumber = "+966501234567",
                RoleId = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            await unitOfWork.Users.AddAsync(user);
            await unitOfWork.SaveChangesAsync();

            Console.WriteLine($"✅ تم إنشاء Super Admin Ahmed بنجاح - ID: {user.Id}");

            // التحقق من كلمة المرور
            Console.WriteLine("\n🔍 التحقق من كلمة المرور...");
            var isValid = passwordService.VerifyPassword(password, hashedPassword);
            Console.WriteLine($"✅ التحقق من كلمة المرور: {isValid}");

            Console.WriteLine("\n🎉 تم إنشاء Super Admin Ahmed بنجاح!");
            Console.WriteLine("=====================================");
            Console.WriteLine($"👤 Username: {user.Username}");
            Console.WriteLine($"📧 Email: {user.Email}");
            Console.WriteLine($"🔑 Password: {password}");
            Console.WriteLine($"🎭 Role ID: {user.RoleId}");
            Console.WriteLine($"✅ Active: {user.IsActive}");
            Console.WriteLine($"📅 Created: {user.CreatedAt}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"تفاصيل الخطأ: {ex.InnerException.Message}");
            }
        }

        Console.WriteLine("\nاضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
}
