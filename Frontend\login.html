<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المتجر الإلكتروني</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <h1>متجر الإلكترونيات</h1>
                </div>
                <p class="subtitle">نظام إدارة المتجر</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        اسم المستخدم
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        required 
                        placeholder="أدخل اسم المستخدم"
                        autocomplete="username"
                    >
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="password-input">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required 
                            placeholder="أدخل كلمة المرور"
                            autocomplete="current-password"
                        >
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="checkbox-container">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <!-- Loading Spinner -->
            <div id="loadingSpinner" class="loading-spinner" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i>
                <p>جاري تسجيل الدخول...</p>
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="error-message" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="errorText"></span>
            </div>

            <!-- Success Message -->
            <div id="successMessage" class="success-message" style="display: none;">
                <i class="fas fa-check-circle"></i>
                <span id="successText"></span>
            </div>

            <!-- Demo Credentials -->
            <div class="demo-credentials">
                <h3><i class="fas fa-info-circle"></i> بيانات تجريبية</h3>
                <div class="demo-user">
                    <strong>Super Admin:</strong>
                    <br>
                    <span>المستخدم: Ahmed</span>
                    <br>
                    <span>كلمة المرور: Ahmed123!</span>
                    <button type="button" onclick="fillDemoCredentials()" class="demo-btn">
                        <i class="fas fa-magic"></i>
                        استخدام البيانات التجريبية
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <p>&copy; 2025 نظام إدارة المتجر الإلكتروني. جميع الحقوق محفوظة.</p>
            <div class="system-status">
                <span id="systemStatus" class="status-indicator">
                    <i class="fas fa-circle"></i>
                    فحص الاتصال...
                </span>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            setupLoginForm();
        });
    </script>
</body>
</html>
