<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ElectroHub Pro 1.0 - نظام إدارة متجر الإلكترونيات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e'
                        },
                        gray: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        .sidebar-item {
            transition: all 0.3s ease;
        }
        
        .sidebar-item:hover {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            transform: translateX(-2px);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* Users page responsive improvements */
        @media (max-width: 640px) {
            .users-stats-card {
                padding: 1rem;
            }

            .users-stats-card .text-2xl {
                font-size: 1.5rem;
            }

            .users-table-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .users-table-actions button {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
        }

        @media (max-width: 768px) {
            .users-header-actions {
                flex-direction: column;
                gap: 0.75rem;
            }

            .users-filters {
                flex-wrap: wrap;
            }
        }

        /* Table responsive improvements */
        .users-table {
            min-width: 600px;
        }

        @media (max-width: 1024px) {
            .users-table th,
            .users-table td {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }
        }

        @media (max-width: 768px) {
            .users-table {
                min-width: 500px;
            }

            .users-table th,
            .users-table td {
                padding: 0.5rem 0.25rem;
                font-size: 0.75rem;
            }
        }

        /* Mobile-specific improvements */
        @media (max-width: 640px) {
            .users-table-actions {
                flex-wrap: wrap;
                gap: 0.25rem;
            }

            .users-table-actions button {
                min-width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0.25rem;
            }

            .users-stats-card .text-2xl,
            .users-stats-card .text-3xl {
                font-size: 1.25rem !important;
            }
        }

        /* Improved scrollbar for table */
        .overflow-x-auto::-webkit-scrollbar {
            height: 6px;
        }

        .overflow-x-auto::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .overflow-x-auto::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .overflow-x-auto::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
            transform: translateY(-1px);
        }
        
        .input-focus {
            transition: all 0.3s ease;
        }
        
        .input-focus:focus {
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .pulse-dot {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 90%;
            max-height: 90%;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }
        
        @keyframes modalSlideIn {
            from { opacity: 0; transform: scale(0.9) translateY(-20px); }
            to { opacity: 1; transform: scale(1) translateY(0); }
        }
        
        .tab-active {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #0ea5e9 0%, #0284c7 100%);
            height: 4px;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #10b981;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            z-index: 1001;
            display: none;
        }

        .notification.error {
            background: #ef4444;
        }

        .notification.warning {
            background: #f59e0b;
        }

        .chart-container {
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body class="bg-gray-50 font-cairo">
    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 fixed w-full top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-4 space-x-reverse">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">ElectroHub Pro</h1>
                        <p class="text-sm text-gray-500">الإصدار 1.0</p>
                    </div>
                </div>
            </div>
            
            <div class="flex items-center space-x-4 space-x-reverse">
                <div class="relative">
                    <input type="text" placeholder="البحث السريع..." class="w-80 px-4 py-2 pr-10 border border-gray-300 rounded-lg input-focus" id="globalSearch">
                    <svg class="w-5 h-5 text-gray-400 absolute right-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                
                <div class="flex items-center space-x-3 space-x-reverse">
                    <button class="relative p-2 text-gray-600 hover:text-primary-600 transition-colors" onclick="showNotifications()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6l-6-6v6z"></path>
                        </svg>
                        <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full pulse-dot"></span>
                    </button>
                    
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="logout()" class="text-gray-500 hover:text-red-600 transition-colors p-2 rounded-lg hover:bg-red-50" title="تسجيل الخروج">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                        </button>
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230ea5e9'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16' font-weight='bold'%3Eأ%3C/text%3E%3C/svg%3E" alt="المستخدم" class="w-8 h-8 rounded-full">
                        <div class="text-right">
                            <p id="currentUserName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="currentUserRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-lg h-screen fixed right-0 overflow-y-auto">
            <nav class="p-4 space-y-2">
                <div class="mb-6">
                    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">لوحة التحكم الرئيسية</h2>
                </div>
                
                <a href="#dashboard" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg bg-primary-50 text-primary-700" data-page="dashboard">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span>لوحة المعلومات</span>
                </a>
                
                <a href="#products" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="products">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <span>إدارة المنتجات</span>
                </a>
                
                <a href="#inventory" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="inventory">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <span>المخزون</span>
                </a>
                
                <a href="#pos" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="pos">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span>نقطة البيع</span>
                </a>
                
                <a href="#suppliers" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="suppliers">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <span>الموردين</span>
                </a>
                
                <a href="#returns" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="returns">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                    </svg>
                    <span>المرتجعات</span>
                </a>
                
                <a href="#reports" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="reports">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>التقارير</span>
                </a>
                
                <div class="pt-4 mt-4 border-t border-gray-200">
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">الإعدادات</h3>
                    <a href="#users" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="users">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <span>المستخدمين</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 mr-64 p-6">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page-content active fade-in">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">مرحباً بك في ElectroHub Pro</h1>
                    <p class="text-gray-600">نظرة شاملة على أداء متجرك الإلكتروني</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">إجمالي المبيعات اليوم</p>
                                <p class="text-2xl font-bold text-gray-900">15,750 ر.س</p>
                                <p class="text-sm text-green-600 mt-1">+12.5% من أمس</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">مبيعات الأسبوع</p>
                                <p class="text-2xl font-bold text-gray-900">98,500 ر.س</p>
                                <p class="text-sm text-red-600 mt-1">-2.1% من الأسبوع الماضي</p>
                            </div>
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">المنتجات المتاحة</p>
                                <p class="text-2xl font-bold text-gray-900">2,847</p>
                                <p class="text-sm text-blue-600 mt-1">في 15 فئة</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">تنبيهات المخزون</p>
                                <p class="text-2xl font-bold text-gray-900 inventory-low-stock">23</p>
                                <p class="text-sm text-orange-600 mt-1">منتج قارب على النفاد</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">صافي الربح الشهري</p>
                                <p class="text-2xl font-bold text-gray-900">89,250 ر.س</p>
                                <p class="text-sm text-green-600 mt-1">+8.2% من الشهر الماضي</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Sales Chart -->
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">مبيعات الأسبوع</h3>
                        <div class="chart-container">
                            <canvas id="salesChart"></canvas>
                        </div>
                    </div>

                    <!-- Top Products -->
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">أكثر المنتجات مبيعاً</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                    <div>
                                        <p class="font-medium text-gray-900">iPhone 15 Pro Max</p>
                                        <p class="text-sm text-gray-500">45 قطعة مباعة</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900">224,955 ر.س</p>
                                    <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                                        <div class="bg-primary-500 h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                    <div>
                                        <p class="font-medium text-gray-900">Samsung Galaxy S24</p>
                                        <p class="text-sm text-gray-500">32 قطعة مباعة</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900">137,568 ر.س</p>
                                    <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                                        <div class="bg-primary-500 h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                    <div>
                                        <p class="font-medium text-gray-900">MacBook Pro 16"</p>
                                        <p class="text-sm text-gray-500">18 قطعة مباعة</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900">179,982 ر.س</p>
                                    <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                                        <div class="bg-primary-500 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">النشاط الأخير</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4 space-x-reverse">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">تم بيع iPhone 15 Pro Max - الفاتورة #1234</p>
                                    <p class="text-xs text-gray-500">منذ 5 دقائق</p>
                                </div>
                                <span class="text-sm font-medium text-green-600">+4,999 ر.س</span>
                            </div>
                            
                            <div class="flex items-center space-x-4 space-x-reverse">
                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">تم إضافة منتج جديد: AirPods Pro 3</p>
                                    <p class="text-xs text-gray-500">منذ 15 دقيقة</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-4 space-x-reverse">
                                <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">تنبيه: مخزون Samsung Galaxy S24 منخفض (8 قطع)</p>
                                    <p class="text-xs text-gray-500">منذ 30 دقيقة</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-4 space-x-reverse">
                                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">تم استلام شحنة جديدة من شركة التقنية المتقدمة</p>
                                    <p class="text-xs text-gray-500">منذ ساعة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Management Page -->
            <div id="products-page" class="page-content fade-in">
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">إدارة المنتجات</h1>
                            <p class="text-gray-600">إضافة وتعديل وإدارة منتجات المتجر</p>
                        </div>
                        <button class="btn-primary text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2 space-x-reverse" onclick="openProductModal()">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>إضافة منتج جديد</span>
                        </button>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">البحث والفلترة</h3>
                        <button onclick="resetProductFilters()" class="text-sm text-gray-500 hover:text-gray-700 flex items-center space-x-1 space-x-reverse">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <span>إعادة تعيين</span>
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                            <input type="text" placeholder="اسم المنتج أو الباركود..." class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" id="productSearch">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" id="categoryFilter">
                                <option value="">جميع الفئات</option>
                                <!-- Categories will be loaded dynamically -->
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المورد</label>
                            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" id="supplierFilter">
                                <option value="">جميع الموردين</option>
                                <!-- Suppliers will be loaded dynamically -->
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">حالة المخزون</label>
                            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" id="stockFilter">
                                <option value="">جميع المنتجات</option>
                                <option value="available">متوفر</option>
                                <option value="low">مخزون منخفض</option>
                                <option value="out">نفد المخزون</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباركود</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">سعر الشراء</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">سعر البيع</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">أقل سعر</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المخزون</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المورد</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="productsTableBody">
                                <!-- Products will be loaded dynamically from database -->
                                <tr id="loadingRow">
                                    <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                                        <div class="flex items-center justify-center">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            جاري تحميل المنتجات...
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">Samsung Galaxy S24 Ultra</div>
                                                <div class="text-sm text-gray-500">512GB - أسود تيتانيوم</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1234567890124</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">الهواتف الذكية</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4,299 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">3,800 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            8 قطع
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">مؤسسة الإلكترونيات</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="editProduct(2)">تعديل</button>
                                            <button class="text-red-600 hover:text-red-900" onclick="deleteProduct(2)">حذف</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="viewProductHistory(2)">السجل</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">MacBook Pro 16"</div>
                                                <div class="text-sm text-gray-500">M3 Pro - 512GB</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1234567890125</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">أجهزة الكمبيوتر</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">9,999 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">9,200 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            نفد المخزون
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">شركة التقنية المتقدمة</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="editProduct(3)">تعديل</button>
                                            <button class="text-red-600 hover:text-red-900" onclick="deleteProduct(3)">حذف</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="viewProductHistory(3)">السجل</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">السابق</button>
                            <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">التالي</button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    عرض <span class="font-medium">1</span> إلى <span class="font-medium">10</span> من <span class="font-medium">97</span> نتيجة
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">السابق</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">2</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</button>
                                    <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">التالي</button>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory Management Page -->
            <div id="inventory-page" class="page-content fade-in">
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">المخزون الرئيسي</h1>
                            <p class="text-gray-600">عرض وإدارة جميع المنتجات في المخزون الواحد</p>
                        </div>
                        <div class="flex space-x-3 space-x-reverse">
                            <button class="btn-primary text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2 space-x-reverse" onclick="refreshInventory()">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span>تحديث المخزون</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Branch Selector -->
                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <label class="text-sm font-medium text-gray-700">الفرع:</label>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="branchSelector">
                            <option value="all">جميع الفروع</option>
                            <option value="main">المحل الرئيسي</option>
                            <option value="north">فرع الشمال</option>
                            <option value="warehouse">المستودع</option>
                        </select>
                        <div class="flex-1"></div>
                        <button class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2 space-x-reverse" onclick="startInventoryCount()">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            </svg>
                            <span>بدء الجرد</span>
                        </button>
                    </div>
                </div>

                <!-- Inventory Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">إجمالي قيمة المخزون</p>
                                <p class="text-2xl font-bold text-gray-900 inventory-total-value">2,847,500 ر.س</p>
                                <p class="text-sm text-blue-600 mt-1">بسعر التكلفة</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">منتجات نفد مخزونها</p>
                                <p class="text-2xl font-bold text-gray-900">12</p>
                                <p class="text-sm text-red-600 mt-1">يحتاج إعادة طلب</p>
                            </div>
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">مخزون منخفض</p>
                                <p class="text-2xl font-bold text-gray-900">23</p>
                                <p class="text-sm text-orange-600 mt-1">أقل من الحد الأدنى</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">إجمالي العناصر</p>
                                <p class="text-2xl font-bold text-gray-900 inventory-total-items">156</p>
                                <p class="text-sm text-green-600 mt-1">دخول وخروج</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Table -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">تفاصيل المخزون</h3>
                            <div class="flex space-x-2 space-x-reverse">
                                <input type="text" placeholder="البحث في المخزون..." class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="inventorySearch">
                                <select class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="inventoryFilter">
                                    <option>جميع الحالات</option>
                                    <option>متوفر</option>
                                    <option>مخزون منخفض</option>
                                    <option>نفد المخزون</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية المتوفرة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحد الأدنى</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">سعر التكلفة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">قيمة المخزون</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">قيمة البيع</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="inventoryTableBody">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">iPhone 15 Pro Max</div>
                                                <div class="text-sm text-gray-500">256GB</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            45 قطعة
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4,200 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">189,000 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="viewMovementHistory('iphone15')">الحركات</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="adjustStock('iphone15')">تعديل</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">Samsung Galaxy S24</div>
                                                <div class="text-sm text-gray-500">512GB</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            8 قطع
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3,600 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">28,800 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="viewMovementHistory('galaxy24')">الحركات</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="adjustStock('galaxy24')">تعديل</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">MacBook Pro 16"</div>
                                                <div class="text-sm text-gray-500">M3 Pro</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            نفد المخزون
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8,500 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">0 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="viewMovementHistory('macbook16')">الحركات</button>
                                            <button class="text-green-600 hover:text-green-900" onclick="reorderProduct('macbook16')">إعادة طلب</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- POS Page -->
            <div id="pos-page" class="page-content fade-in">
                <div class="mb-6">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">نقطة البيع</h1>
                    <p class="text-gray-600">واجهة البيع السريع والفعال</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Product Search and List -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="mb-6">
                                <div class="relative">
                                    <input type="text" placeholder="البحث بالاسم أو مسح الباركود..." class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg input-focus text-lg" id="posSearch">
                                    <svg class="w-6 h-6 text-gray-400 absolute right-3 top-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>

                            <!-- Product Categories -->
                            <div class="mb-6">
                                <div class="flex space-x-2 space-x-reverse overflow-x-auto pb-2">
                                    <button class="tab-active px-4 py-2 rounded-lg font-medium whitespace-nowrap" onclick="filterPOSProducts('all')">الكل</button>
                                    <button class="px-4 py-2 rounded-lg font-medium text-gray-600 hover:bg-gray-100 whitespace-nowrap" onclick="filterPOSProducts('phones')">الهواتف</button>
                                    <button class="px-4 py-2 rounded-lg font-medium text-gray-600 hover:bg-gray-100 whitespace-nowrap" onclick="filterPOSProducts('computers')">الكمبيوتر</button>
                                    <button class="px-4 py-2 rounded-lg font-medium text-gray-600 hover:bg-gray-100 whitespace-nowrap" onclick="filterPOSProducts('accessories')">الإكسسوارات</button>
                                    <button class="px-4 py-2 rounded-lg font-medium text-gray-600 hover:bg-gray-100 whitespace-nowrap" onclick="filterPOSProducts('appliances')">الأجهزة المنزلية</button>
                                </div>
                            </div>

                            <!-- Products Grid -->
                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" id="productsGrid">
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="addToCart('iPhone 15 Pro Max', 4999)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h4 class="font-medium text-sm text-gray-900 mb-1">iPhone 15 Pro Max</h4>
                                    <p class="text-xs text-gray-500 mb-2">256GB</p>
                                    <p class="font-bold text-primary-600">4,999 ر.س</p>
                                    <p class="text-xs text-green-600">متوفر: 45</p>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="addToCart('Samsung Galaxy S24', 4299)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h4 class="font-medium text-sm text-gray-900 mb-1">Samsung Galaxy S24</h4>
                                    <p class="text-xs text-gray-500 mb-2">512GB</p>
                                    <p class="font-bold text-primary-600">4,299 ر.س</p>
                                    <p class="text-xs text-yellow-600">متوفر: 8</p>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer opacity-50" onclick="showNotification('المنتج غير متوفر حالياً', 'error')">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h4 class="font-medium text-sm text-gray-900 mb-1">MacBook Pro 16"</h4>
                                    <p class="text-xs text-gray-500 mb-2">M3 Pro</p>
                                    <p class="font-bold text-gray-400">9,999 ر.س</p>
                                    <p class="text-xs text-red-600">نفد المخزون</p>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="addToCart('AirPods Pro 3', 899)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h4 class="font-medium text-sm text-gray-900 mb-1">AirPods Pro 3</h4>
                                    <p class="text-xs text-gray-500 mb-2">USB-C</p>
                                    <p class="font-bold text-primary-600">899 ر.س</p>
                                    <p class="text-xs text-green-600">متوفر: 32</p>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="addToCart('iPad Air 11', 2299)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h4 class="font-medium text-sm text-gray-900 mb-1">iPad Air 11"</h4>
                                    <p class="text-xs text-gray-500 mb-2">M2 - 256GB</p>
                                    <p class="font-bold text-primary-600">2,299 ر.س</p>
                                    <p class="text-xs text-green-600">متوفر: 18</p>
                                </div>

                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="addToCart('Apple Watch Series 9', 1599)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h4 class="font-medium text-sm text-gray-900 mb-1">Apple Watch Series 9</h4>
                                    <p class="text-xs text-gray-500 mb-2">45mm GPS</p>
                                    <p class="font-bold text-primary-600">1,599 ر.س</p>
                                    <p class="text-xs text-green-600">متوفر: 12</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart and Checkout -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 sticky top-24">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">سلة المشتريات</h3>
                            
                            <!-- Cart Items -->
                            <div class="space-y-3 mb-6 max-h-64 overflow-y-auto" id="cart-items">
                                <div class="text-center text-gray-500 py-8">
                                    <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                                    </svg>
                                    <p>السلة فارغة</p>
                                    <p class="text-sm">اختر المنتجات لإضافتها</p>
                                </div>
                            </div>

                            <!-- Discount Section -->
                            <div class="border-t border-gray-200 pt-4 mb-4">
                                <div class="flex items-center space-x-2 space-x-reverse mb-2">
                                    <input type="number" placeholder="خصم %" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg input-focus text-sm" id="discountPercent">
                                    <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium" onclick="applyDiscount()">تطبيق</button>
                                </div>
                                <input type="number" placeholder="خصم مبلغ ثابت (ر.س)" class="w-full px-3 py-2 border border-gray-300 rounded-lg input-focus text-sm" id="discountAmount">
                            </div>

                            <!-- Totals -->
                            <div class="space-y-2 mb-6">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">المجموع الفرعي:</span>
                                    <span id="subtotal">0 ر.س</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">الخصم:</span>
                                    <span class="text-red-600" id="discount">0 ر.س</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">ضريبة القيمة المضافة (15%):</span>
                                    <span id="tax">0 ر.س</span>
                                </div>
                                <div class="border-t border-gray-200 pt-2">
                                    <div class="flex justify-between font-semibold text-lg">
                                        <span>الإجمالي:</span>
                                        <span class="text-primary-600" id="total">0 ر.س</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Methods -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">طريقة الدفع</label>
                                <div class="space-y-2">
                                    <button class="payment-method w-full border-2 border-primary-200 bg-primary-50 text-primary-700 p-3 rounded-lg text-sm font-medium active hover:bg-primary-100 transition-colors flex items-center justify-center" data-method="cash">
                                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        نقداً
                                    </button>
                                    <button class="payment-method w-full border-2 border-gray-200 text-gray-700 p-3 rounded-lg text-sm font-medium hover:border-gray-300 hover:bg-gray-50 transition-colors flex items-center justify-center" data-method="card">
                                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                        بطاقة ائتمان
                                    </button>
                                    <button class="payment-method w-full border-2 border-gray-200 text-gray-700 p-3 rounded-lg text-sm font-medium hover:border-gray-300 hover:bg-gray-50 transition-colors flex items-center justify-center" data-method="transfer">
                                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                        تحويل بنكي
                                    </button>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="space-y-3">
                                <button onclick="processPayment('cash')" class="w-full bg-primary-600 hover:bg-primary-700 text-white py-4 rounded-lg font-semibold transition-colors shadow-sm hover:shadow-md">
                                    <svg class="w-5 h-5 inline-block ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    إتمام البيع
                                </button>
                                <div class="grid grid-cols-2 gap-3 mt-3">
                                    <button onclick="clearCart()" class="px-4 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                                        <svg class="w-4 h-4 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        مسح السلة
                                    </button>
                                    <button onclick="holdTransaction()" class="px-4 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                                        <svg class="w-4 h-4 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        تعليق
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Suppliers Page -->
            <div id="suppliers-page" class="page-content fade-in">
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">إدارة الموردين</h1>
                            <p class="text-gray-600">إدارة معلومات الموردين وتتبع المشتريات</p>
                        </div>
                        <button class="btn-primary text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2 space-x-reverse" onclick="openModal('addSupplierModal')">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>إضافة مورد جديد</span>
                        </button>
                    </div>
                </div>

                <!-- Suppliers Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">إجمالي الموردين</p>
                                <p class="text-2xl font-bold text-gray-900">12</p>
                                <p class="text-sm text-blue-600 mt-1">موردين نشطين</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">مشتريات الشهر</p>
                                <p class="text-2xl font-bold text-gray-900">847,500 ر.س</p>
                                <p class="text-sm text-green-600 mt-1">+15.2% من الشهر الماضي</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">فواتير معلقة</p>
                                <p class="text-2xl font-bold text-gray-900">3</p>
                                <p class="text-sm text-orange-600 mt-1">تحتاج مراجعة</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">أفضل مورد</p>
                                <p class="text-lg font-bold text-gray-900">شركة التقنية</p>
                                <p class="text-sm text-purple-600 mt-1">45% من المشتريات</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Suppliers Table -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">قائمة الموردين</h3>
                            <div class="flex space-x-2 space-x-reverse">
                                <input type="text" placeholder="البحث عن مورد..." class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="supplierSearch">
                                <select class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="supplierStatusFilter">
                                    <option>جميع الحالات</option>
                                    <option>نشط</option>
                                    <option>غير نشط</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المورد</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">معلومات الاتصال</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي المشتريات</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر شراء</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                                <span class="text-primary-600 font-medium text-sm">ش.ت</span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">شركة التقنية المتقدمة</div>
                                                <div class="text-sm text-gray-500">مورد رئيسي</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">+966 11 123 4567</div>
                                        <div class="text-sm text-gray-500"><EMAIL></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">1,247,500 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            نشط
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="editSupplier(1)">تعديل</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="viewSupplierHistory(1)">السجل</button>
                                            <button class="text-green-600 hover:text-green-900" onclick="createPurchaseOrder(1)">طلب شراء</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <span class="text-blue-600 font-medium text-sm">م.إ</span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">مؤسسة الإلكترونيات</div>
                                                <div class="text-sm text-gray-500">مورد ثانوي</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">+966 12 987 6543</div>
                                        <div class="text-sm text-gray-500"><EMAIL></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">687,200 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-10</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            نشط
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="editSupplier(2)">تعديل</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="viewSupplierHistory(2)">السجل</button>
                                            <button class="text-green-600 hover:text-green-900" onclick="createPurchaseOrder(2)">طلب شراء</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                                <span class="text-gray-600 font-medium text-sm">م.أ</span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">متجر الأجهزة</div>
                                                <div class="text-sm text-gray-500">مورد محلي</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">+966 13 555 7890</div>
                                        <div class="text-sm text-gray-500"><EMAIL></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">234,800 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-12-28</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            غير نشط
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="editSupplier(3)">تعديل</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="viewSupplierHistory(3)">السجل</button>
                                            <button class="text-gray-400 cursor-not-allowed">طلب شراء</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Returns Page -->
            <div id="returns-page" class="page-content fade-in">
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">إدارة المرتجعات</h1>
                            <p class="text-gray-600">تسجيل ومتابعة مرتجعات العملاء والموردين</p>
                        </div>
                        <div class="flex space-x-3 space-x-reverse">
                            <button class="btn-primary text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2 space-x-reverse" onclick="openModal('customerReturnModal')">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                                <span>مرتجع عميل</span>
                            </button>
                            <button class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2 space-x-reverse" onclick="openModal('supplierReturnModal')">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H9m0 0l3 3m-3-3l3-3m5 14v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 002 2h8a2 2 0 002-2z"></path>
                                </svg>
                                <span>مرتجع للمورد</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Returns Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">مرتجعات اليوم</p>
                                <p class="text-2xl font-bold text-gray-900">8</p>
                                <p class="text-sm text-blue-600 mt-1">بقيمة 12,450 ر.س</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">مرتجعات الشهر</p>
                                <p class="text-2xl font-bold text-gray-900">156</p>
                                <p class="text-sm text-orange-600 mt-1">بقيمة 287,650 ر.س</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">معدل المرتجعات</p>
                                <p class="text-2xl font-bold text-gray-900">2.8%</p>
                                <p class="text-sm text-green-600 mt-1">ضمن المعدل الطبيعي</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">مرتجعات معلقة</p>
                                <p class="text-2xl font-bold text-gray-900">5</p>
                                <p class="text-sm text-red-600 mt-1">تحتاج معالجة</p>
                            </div>
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Returns Table -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">سجل المرتجعات</h3>
                            <div class="flex space-x-2 space-x-reverse">
                                <input type="text" placeholder="البحث في المرتجعات..." class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="returnsSearch">
                                <select class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="returnsTypeFilter">
                                    <option>جميع الأنواع</option>
                                    <option>مرتجع عميل</option>
                                    <option>مرتجع للمورد</option>
                                </select>
                                <select class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="returnsStatusFilter">
                                    <option>جميع الحالات</option>
                                    <option>مكتمل</option>
                                    <option>معلق</option>
                                    <option>مرفوض</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم المرتجع</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القيمة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السبب</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary-600">#RET-001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            مرتجع عميل
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">iPhone 15 Pro Max</div>
                                        <div class="text-sm text-gray-500">256GB - تيتانيوم</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">4,999 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">عيب في الشاشة</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            مكتمل
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="viewReturnDetails(1)">التفاصيل</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="printReturnReceipt(1)">طباعة</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary-600">#RET-002</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                            مرتجع للمورد
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">Samsung Galaxy S24</div>
                                        <div class="text-sm text-gray-500">512GB - أسود</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">12,897 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">منتج تالف</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-14</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            معلق
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="viewReturnDetails(2)">التفاصيل</button>
                                            <button class="text-green-600 hover:text-green-900" onclick="approveReturn(2)">موافقة</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary-600">#RET-003</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            مرتجع عميل
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">AirPods Pro 3</div>
                                        <div class="text-sm text-gray-500">USB-C</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">899 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">لا يعمل الصوت</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-13</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            مكتمل
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900" onclick="viewReturnDetails(3)">التفاصيل</button>
                                            <button class="text-blue-600 hover:text-blue-900" onclick="printReturnReceipt(3)">طباعة</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Reports Page -->
            <div id="reports-page" class="page-content fade-in">
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">التقارير والتحليلات</h1>
                            <p class="text-gray-600">تقارير شاملة لمتابعة الأداء واتخاذ القرارات</p>
                        </div>
                        <div class="flex space-x-3 space-x-reverse">
                            <select class="px-4 py-2 border border-gray-300 rounded-lg input-focus" id="reportPeriod">
                                <option>اليوم</option>
                                <option>الأسبوع</option>
                                <option>الشهر</option>
                                <option>الربع</option>
                                <option>السنة</option>
                                <option>فترة مخصصة</option>
                            </select>
                            <button class="btn-primary text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2 space-x-reverse" onclick="exportReports()">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <span>تصدير التقارير</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Categories -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100 cursor-pointer" onclick="generateReport('sales')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <span class="text-sm text-gray-500">تقرير المبيعات</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">تحليل المبيعات</h3>
                        <p class="text-sm text-gray-600 mb-4">تقرير مفصل عن المبيعات والأرباح</p>
                        <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold text-green-600">847,500 ر.س</span>
                            <span class="text-sm text-green-600">+15.2%</span>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100 cursor-pointer" onclick="generateReport('inventory')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <span class="text-sm text-gray-500">تقرير المخزون</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">تقييم المخزون</h3>
                        <p class="text-sm text-gray-600 mb-


4">قيمة المخزون الحالي وحالة التوفر</p>
                        <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold text-blue-600">2,847,500 ر.س</span>
                            <span class="text-sm text-orange-600">23 تنبيه</span>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100 cursor-pointer" onclick="generateReport('profit')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <span class="text-sm text-gray-500">الأرباح والخسائر</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">الأرباح والخسائر</h3>
                        <p class="text-sm text-gray-600 mb-4">تحليل مفصل للأرباح والمصروفات</p>
                        <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold text-purple-600">289,750 ر.س</span>
                            <span class="text-sm text-green-600">+8.7%</span>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100 cursor-pointer" onclick="generateReport('products')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <span class="text-sm text-gray-500">تحليل المنتجات</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">أداء المنتجات</h3>
                        <p class="text-sm text-gray-600 mb-4">المنتجات الأكثر مبيعاً وربحية</p>
                        <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold text-orange-600">2,847</span>
                            <span class="text-sm text-blue-600">منتج</span>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100 cursor-pointer" onclick="generateReport('discounts')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                            </div>
                            <span class="text-sm text-gray-500">تقرير الخصومات</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">الخصومات</h3>
                        <p class="text-sm text-gray-600 mb-4">إجمالي الخصومات الممنوحة</p>
                        <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold text-red-600">47,250 ر.س</span>
                            <span class="text-sm text-red-600">5.6%</span>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100 cursor-pointer" onclick="generateReport('expenses')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <span class="text-sm text-gray-500">تقرير المصروفات</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">المصروفات</h3>
                        <p class="text-sm text-gray-600 mb-4">إجمالي المصروفات التشغيلية</p>
                        <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold text-gray-600">125,400 ر.س</span>
                            <span class="text-sm text-gray-600">14.8%</span>
                        </div>
                    </div>
                </div>

                <!-- Detailed Report View -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100" id="reportDetails">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">تقرير مفصل</h3>
                        <p class="text-sm text-gray-600">اختر نوع التقرير من الأعلى لعرض التفاصيل</p>
                    </div>
                    <div class="p-6">
                        <div class="text-center text-gray-500 py-12">
                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <p class="text-lg font-medium">لا يوجد تقرير محدد</p>
                            <p class="text-sm">اختر نوع التقرير لعرض البيانات التفصيلية</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Management Page -->
            <div id="users-page" class="page-content fade-in">
                <!-- Page Header -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
                    <div class="mb-4 sm:mb-0">
                        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">إدارة المستخدمين</h1>
                        <p class="text-gray-600">إدارة المستخدمين والصلاحيات في النظام</p>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button class="btn-secondary text-gray-700 px-4 py-2 rounded-lg font-medium flex items-center justify-center space-x-2 space-x-reverse border border-gray-300 hover:bg-gray-50 transition-colors" onclick="window.usersManager?.exportUsers()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span>تصدير</span>
                        </button>
                        <button class="btn-secondary text-gray-700 px-4 py-2 rounded-lg font-medium flex items-center justify-center space-x-2 space-x-reverse border border-gray-300 hover:bg-gray-50 transition-colors" onclick="openModal('userFiltersModal')">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"></path>
                            </svg>
                            <span>فلاتر متقدمة</span>
                        </button>
                        <button class="btn-primary text-white px-6 py-2 rounded-lg font-medium flex items-center justify-center space-x-2 space-x-reverse hover:bg-primary-700 transition-colors" onclick="openModal('addUserModal')">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>إضافة مستخدم جديد</span>
                        </button>
                    </div>
                </div>

                <!-- Users Stats -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8" id="userStatsCards">
                    <div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm card-hover border border-gray-100 transition-all duration-200 users-stats-card">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-600 mb-1">إجمالي المستخدمين</p>
                                <p class="text-2xl lg:text-3xl font-bold text-gray-900 mb-1">8</p>
                                <p class="text-xs lg:text-sm text-blue-600">6 مستخدمين نشطين</p>
                            </div>
                            <div class="w-10 h-10 lg:w-12 lg:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 lg:w-6 lg:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm card-hover border border-gray-100 transition-all duration-200 users-stats-card">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-600 mb-1">المديرين</p>
                                <p class="text-2xl lg:text-3xl font-bold text-gray-900 mb-1">2</p>
                                <p class="text-xs lg:text-sm text-purple-600">صلاحيات كاملة</p>
                            </div>
                            <div class="w-10 h-10 lg:w-12 lg:h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 lg:w-6 lg:h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm card-hover border border-gray-100 transition-all duration-200 users-stats-card">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-600 mb-1">موظفي المبيعات</p>
                                <p class="text-2xl lg:text-3xl font-bold text-gray-900 mb-1">3</p>
                                <p class="text-xs lg:text-sm text-green-600">نقطة البيع</p>
                            </div>
                            <div class="w-10 h-10 lg:w-12 lg:h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 lg:w-6 lg:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm card-hover border border-gray-100 transition-all duration-200 users-stats-card">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-600 mb-1">آخر تسجيل دخول</p>
                                <p class="text-lg lg:text-xl font-bold text-gray-900 mb-1">أحمد محمد</p>
                                <p class="text-xs lg:text-sm text-gray-600">منذ 5 دقائق</p>
                            </div>
                            <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 lg:w-6 lg:h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Active Sessions -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 mb-8">
                    <div class="p-4 lg:p-6 border-b border-gray-200">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                            <h3 class="text-lg font-semibold text-gray-900">الجلسات النشطة</h3>
                            <button class="text-sm text-primary-600 hover:text-primary-800 transition-colors flex items-center space-x-1 space-x-reverse" onclick="window.usersManager?.refreshActiveSessions()">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span>تحديث</span>
                            </button>
                        </div>
                    </div>
                    <div class="p-4 lg:p-6">
                        <div id="activeSessionsList" class="space-y-3 lg:space-y-4">
                            <!-- Active sessions will be loaded here -->
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 lg:p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                <div class="flex items-center space-x-3 space-x-reverse mb-2 sm:mb-0">
                                    <div class="w-8 h-8 lg:w-10 lg:h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                        <div class="w-2 h-2 lg:w-3 lg:h-3 bg-green-500 rounded-full animate-pulse"></div>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                                        <p class="text-xs text-gray-500">192.168.1.100 • Chrome على Windows</p>
                                    </div>
                                </div>
                                <div class="flex flex-col sm:items-end space-y-1">
                                    <p class="text-sm text-gray-900">منذ 5 دقائق</p>
                                    <button class="text-xs text-red-600 hover:text-red-800 transition-colors">إنهاء الجلسة</button>
                                </div>
                            </div>
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 lg:p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                <div class="flex items-center space-x-3 space-x-reverse mb-2 sm:mb-0">
                                    <div class="w-8 h-8 lg:w-10 lg:h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                        <div class="w-2 h-2 lg:w-3 lg:h-3 bg-green-500 rounded-full animate-pulse"></div>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">سارة أحمد</p>
                                        <p class="text-xs text-gray-500">192.168.1.105 • Firefox على Mac</p>
                                    </div>
                                </div>
                                <div class="flex flex-col sm:items-end space-y-1">
                                    <p class="text-sm text-gray-900">منذ ساعة</p>
                                    <button class="text-xs text-red-600 hover:text-red-800 transition-colors">إنهاء الجلسة</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Indicator -->
                <div id="usersLoading" class="hidden">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
                        <div class="flex items-center justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                            <span class="mr-3 text-gray-600">جاري تحميل المستخدمين...</span>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="p-4 lg:p-6 border-b border-gray-200">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                            <h3 class="text-lg font-semibold text-gray-900">قائمة المستخدمين</h3>
                            <div class="flex flex-col sm:flex-row gap-2 lg:gap-3">
                                <input type="text" placeholder="البحث عن مستخدم..." class="px-3 py-2 border border-gray-300 rounded-lg input-focus text-sm flex-1 sm:flex-none sm:w-48" id="userSearch">
                                <div class="flex gap-2">
                                    <select class="px-3 py-2 border border-gray-300 rounded-lg input-focus text-sm flex-1 sm:flex-none" id="roleFilter">
                                        <option value="">جميع الأدوار</option>
                                        <option value="مدير">مدير</option>
                                        <option value="موظف مبيعات">موظف مبيعات</option>
                                        <option value="مسؤول مخزون">مسؤول مخزون</option>
                                    </select>
                                    <select class="px-3 py-2 border border-gray-300 rounded-lg input-focus text-sm flex-1 sm:flex-none" id="statusFilter">
                                        <option value="">جميع الحالات</option>
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="online">متصل</option>
                                        <option value="offline">غير متصل</option>
                                    </select>
                                    <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 flex items-center space-x-1 space-x-reverse text-sm transition-colors whitespace-nowrap" onclick="window.usersManager?.toggleBulkActions()">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                        </svg>
                                        <span class="hidden sm:inline">إجراءات متعددة</span>
                                        <span class="sm:hidden">إجراءات</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full min-w-full users-table">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-3 lg:px-6 py-3 lg:py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                                        <input type="checkbox" id="selectAllUsers" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500 hidden ml-2" onchange="window.usersManager?.toggleSelectAll(this.checked)">
                                        المستخدم
                                    </th>
                                    <th class="px-3 lg:px-6 py-3 lg:py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">الدور</th>
                                    <th class="px-3 lg:px-6 py-3 lg:py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px] hidden sm:table-cell">البريد الإلكتروني</th>
                                    <th class="px-3 lg:px-6 py-3 lg:py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] hidden md:table-cell">رقم الهاتف</th>
                                    <th class="px-3 lg:px-6 py-3 lg:py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] hidden lg:table-cell">تاريخ التسجيل</th>
                                    <th class="px-3 lg:px-6 py-3 lg:py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[140px] hidden md:table-cell">آخر تسجيل دخول</th>
                                    <th class="px-3 lg:px-6 py-3 lg:py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">الحالة</th>
                                    <th class="px-3 lg:px-6 py-3 lg:py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="usersTableBody">
                                <!-- Users will be loaded here by JavaScript -->
                                <tr>
                                    <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-4"></div>
                                            <p class="text-lg font-medium">جاري تحميل المستخدمين...</p>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    
    <!-- Add Product Modal -->
    <div id="productModal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex items-center justify-between mb-6">
                <h2 id="productModalTitle" class="text-xl font-bold text-gray-900">إضافة منتج جديد</h2>
                <button onclick="closeProductModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form id="productForm" class="space-y-4" onsubmit="return handleProductSubmit(event)">
                <input type="hidden" id="productId" name="productId">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المنتج *</label>
                        <input type="text" id="productName" name="name" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="مثال: iPhone 15 Pro Max">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الباركود *</label>
                        <input type="text" id="productBarcode" name="barcode" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="1234567890123">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الفئة *</label>
                        <select id="productCategory" name="categoryId" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر الفئة</option>
                            <!-- Categories will be loaded dynamically -->
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المورد الافتراضي *</label>
                        <select id="productSupplier" name="supplierId" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر المورد</option>
                            <!-- Suppliers will be loaded dynamically -->
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">سعر الشراء (ر.س) *</label>
                        <input type="number" id="productPurchasePrice" name="purchasePrice" required step="0.01" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="3500.00">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">سعر البيع (ر.س) *</label>
                        <input type="number" id="productSalePrice" name="salePrice" required step="0.01" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="4999.00">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">أقل سعر بيع (ر.س) *</label>
                        <input type="number" id="productMinPrice" name="minPrice" required step="0.01" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="4500.00">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الكمية الحالية *</label>
                        <input type="number" id="productQuantity" name="quantity" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="0">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى للمخزون</label>
                        <input type="number" id="productMinStock" name="minStock" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="5">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">صورة المنتج</label>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <input type="file" id="productImage" name="image" accept="image/*" class="hidden" onchange="previewImage(this)">
                        <button type="button" onclick="document.getElementById('productImage').click()" class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2 space-x-reverse">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span>اختر صورة</span>
                        </button>
                        <div id="imagePreview" class="hidden">
                            <img id="previewImg" src="" alt="معاينة الصورة" class="w-16 h-16 object-cover rounded-lg border">
                        </div>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف المنتج</label>
                    <textarea id="productDescription" name="description" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" rows="3" placeholder="وصف تفصيلي للمنتج..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeProductModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" id="productSubmitBtn" class="btn-primary text-white px-6 py-2 rounded-lg">
                        حفظ المنتج
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Transfer Modal -->
    <div id="transferModal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">تحويل بين الفروع</h2>
                <button onclick="closeModal('transferModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">من الفرع *</label>
                        <select required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر الفرع</option>
                            <option>المحل الرئيسي</option>
                            <option>فرع الشمال</option>
                            <option>المستودع</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">إلى الفرع *</label>
                        <select required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر الفرع</option>
                            <option>المحل الرئيسي</option>
                            <option>فرع الشمال</option>
                            <option>المستودع</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">المنتج *</label>
                    <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="البحث عن المنتج أو مسح الباركود">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الكمية *</label>
                    <input type="number" required min="1" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="1">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" rows="3" placeholder="ملاحظات حول التحويل..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('transferModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        تأكيد التحويل
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Purchase Modal -->
    <div id="purchaseModal" class="modal">
        <div class="modal-content w-full max-w-4xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">فاتورة شراء جديدة</h2>
                <button onclick="closeModal('purchaseModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المورد *</label>
                        <select required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر المورد</option>
                            <option>شركة التقنية المتقدمة</option>
                            <option>مؤسسة الإلكترونيات</option>
                            <option>متجر الأجهزة</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الشراء *</label>
                        <input type="date" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الفرع المستلم *</label>
                        <select required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر الفرع</option>
                            <option>المحل الرئيسي</option>
                            <option>فرع الشمال</option>
                            <option>المستودع</option>
                        </select>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">المنتجات المشتراة</h3>
                    <div class="space-y-3" id="purchaseItems">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-3 items-end">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">المنتج</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg input-focus" placeholder="البحث أو الباركود">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الكمية</label>
                                <input type="number" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-lg input-focus" placeholder="1">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">سعر التكلفة</label>
                                <input type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-lg input-focus" placeholder="0.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الإجمالي</label>
                                <input type="text" readonly class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50" placeholder="0.00">
                            </div>
                            <div>
                                <button type="button" class="w-full px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">حذف</button>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="mt-3 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700" onclick="addPurchaseItem()">
                        إضافة منتج
                    </button>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center text-lg font-semibold">
                        <span>الإجمالي الكلي:</span>
                        <span class="text-primary-600">0.00 ر.س</span>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('purchaseModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        حفظ فاتورة الشراء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Supplier Modal -->
    <div id="addSupplierModal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">إضافة مورد جديد</h2>
                <button onclick="closeModal('addSupplierModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المورد *</label>
                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="مثال: شركة التقنية المتقدمة">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع المورد</label>
                        <select class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option>مورد رئيسي</option>
                            <option>مورد ثانوي</option>
                            <option>مورد محلي</option>
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                        <input type="tel" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="+966 11 123 4567">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="<EMAIL>">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <textarea class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" rows="3" placeholder="العنوان الكامل للمورد..."></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" rows="2" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('addSupplierModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        حفظ المورد
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Customer Return Modal -->
    <div id="customerReturnModal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">مرتجع عميل</h2>
                <button onclick="closeModal('customerReturnModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الفاتورة الأصلية</label>
                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="#INV-001">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ المرتجع *</label>
                        <input type="date" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">المنتج *</label>
                    <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="البحث عن المنتج أو مسح الباركود">
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الكمية *</label>
                        <input type="number" required min="1" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="1">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">قيمة المرتجع (ر.س)</label>
                        <input type="number" step="0.01" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="0.00">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">سبب المرتجع *</label>
                    <select required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                        <option value="">اختر السبب</option>
                        <option>عيب في المنتج</option>
                        <option>لا يعمل بشكل صحيح</option>
                        <option>تغيير رأي العميل</option>
                        <option>منتج خاطئ</option>
                        <option>تلف أثناء الشحن</option>
                        <option>أخرى</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" rows="3" placeholder="تفاصيل إضافية حول المرتجع..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('customerReturnModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        تسجيل المرتجع
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Supplier Return Modal -->
    <div id="supplierReturnModal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">مرتجع للمورد</h2>
                <button onclick="closeModal('supplierReturnModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المورد *</label>
                        <select required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر المورد</option>
                            <option>شركة التقنية المتقدمة</option>
                            <option>مؤسسة الإلكترونيات</option>
                            <option>متجر الأجهزة</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ المرتجع *</label>
                        <input type="date" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">المنتج *</label>
                    <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="البحث عن المنتج أو مسح الباركود">
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الكمية *</label>
                        <input type="number" required min="1" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="1">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">قيمة المرتجع (ر.س)</label>
                        <input type="number" step="0.01" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="0.00">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">سبب المرتجع *</label>
                    <select required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                        <option value="">اختر السبب</option>
                        <option>منتج تالف</option>
                        <option>منتج خاطئ</option>
                        <option>انتهاء صلاحية</option>
                        <option>عدم مطابقة المواصفات</option>
                        <option>أخرى</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" rows="3" placeholder="تفاصيل إضافية حول المرتجع..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('supplierReturnModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        تسجيل المرتجع
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">إضافة مستخدم جديد</h2>
                <button onclick="closeModal('addUserModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form class="space-y-4" id="addUserForm">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                        <input type="text" name="fullName" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="مثال: أحمد محمد">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم *</label>
                        <input type="text" name="username" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="ahmed.mohammed">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                        <input type="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="tel" name="phoneNumber" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="+966 50 123 4567">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الدور *</label>
                        <select name="roleId" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر الدور</option>
                            <option value="1">مدير</option>
                            <option value="2">موظف مبيعات</option>
                            <option value="3">مسؤول مخزون</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الفرع</label>
                        <select name="branchId" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">جميع الفروع</option>
                            <option value="1">المحل الرئيسي</option>
                            <option value="2">فرع الشمال</option>
                            <option value="3">المستودع</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور *</label>
                        <input type="password" name="password" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="كلمة مرور قوية">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور *</label>
                        <input type="password" name="confirmPassword" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus" placeholder="تأكيد كلمة المرور">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">الصلاحيات</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">إدارة المنتجات</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">إدارة المخزون</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" checked>
                            <span class="mr-2 text-sm text-gray-700">نقطة البيع</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">إدارة الموردين</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">المرتجعات</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">التقارير</span>
                        </label>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('addUserModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        إضافة المستخدم
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Advanced Filters Modal -->
    <div id="userFiltersModal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">فلاتر متقدمة</h2>
                <button onclick="closeModal('userFiltersModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form class="space-y-4" id="advancedFiltersForm">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ التسجيل من</label>
                        <input type="date" name="registrationDateFrom" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ التسجيل إلى</label>
                        <input type="date" name="registrationDateTo" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">آخر تسجيل دخول من</label>
                        <input type="date" name="lastLoginFrom" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">آخر تسجيل دخول إلى</label>
                        <input type="date" name="lastLoginTo" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">حالة المستخدم</label>
                        <select name="userStatus" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">حالة الاتصال</label>
                        <select name="connectionStatus" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">جميع الحالات</option>
                            <option value="online">متصل</option>
                            <option value="offline">غير متصل</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الدور</label>
                        <select name="roleFilter" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">جميع الأدوار</option>
                            <option value="1">مدير</option>
                            <option value="2">موظف مبيعات</option>
                            <option value="3">مسؤول مخزون</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">الصلاحيات</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions" value="products" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">إدارة المنتجات</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions" value="inventory" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">إدارة المخزون</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions" value="sales" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">نقطة البيع</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions" value="suppliers" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">إدارة الموردين</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions" value="reports" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">التقارير</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions" value="users" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-700">إدارة المستخدمين</span>
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="window.usersManager?.clearFilters()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        مسح الفلاتر
                    </button>
                    <button type="button" onclick="closeModal('userFiltersModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        تطبيق الفلاتر
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div id="bulkActionsModal" class="modal">
        <div class="modal-content w-full max-w-lg">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">إجراءات متعددة</h2>
                <button onclick="closeModal('bulkActionsModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-4">
                <p class="text-sm text-gray-600">تم تحديد <span id="selectedUsersCount">0</span> مستخدم</p>

                <div class="space-y-2">
                    <button class="w-full text-right px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-3 space-x-reverse" onclick="window.usersManager?.bulkActivate()">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>تفعيل المستخدمين المحددين</span>
                    </button>

                    <button class="w-full text-right px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-3 space-x-reverse" onclick="window.usersManager?.bulkDeactivate()">
                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>إلغاء تفعيل المستخدمين المحددين</span>
                    </button>

                    <button class="w-full text-right px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-3 space-x-reverse" onclick="window.usersManager?.bulkResetPassword()">
                        <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v-2l-4-4L7.257 8.257a6 6 0 018.486-1.514zM11 19H6.5a2.5 2.5 0 010-5H11m0 5v-5m0 5h5.5a2.5 2.5 0 000-5H11m0 0V9"></path>
                        </svg>
                        <span>إعادة تعيين كلمات المرور</span>
                    </button>

                    <button class="w-full text-right px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-3 space-x-reverse" onclick="window.usersManager?.bulkExport()">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>تصدير المستخدمين المحددين</span>
                    </button>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('bulkActionsModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let cart = [];
        let currentUser = {
            name: 'أحمد محمد',
            role: 'مدير النظام',
            permissions: ['all']
        };

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // Show selected page
            document.getElementById(pageId + '-page').classList.add('active');
            
            // Update sidebar active state
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('bg-primary-50', 'text-primary-700');
                item.classList.add('text-gray-700');
            });
            
            document.querySelector(`[data-page="${pageId}"]`).classList.add('bg-primary-50', 'text-primary-700');
            document.querySelector(`[data-page="${pageId}"]`).classList.remove('text-gray-700');
        }

        // Initialize page navigation
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageId = this.getAttribute('data-page');
                    showPage(pageId);
                });
            });
        });

        // Modal functionality
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Close modal when clicking outside
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('active');
                }
            });
        });

        // Notification system
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        // POS Cart functionality
        function addToCart(productName, price) {
            const existingItem = cart.find(item => item.name === productName);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    name: productName,
                    price: price,
                    quantity: 1
                });
            }
            
            updateCartDisplay();
            updateTotals();
            showNotification(`تم إضافة ${productName} إلى السلة`);
        }

        function removeFromCart(productName) {
            cart = cart.filter(item => item.name !== productName);
            updateCartDisplay();
            updateTotals();
            showNotification(`تم حذف ${productName} من السلة`, 'warning');
        }

        function updateQuantity(productName, newQuantity) {
            const item = cart.find(item => item.name === productName);
            if (item) {
                if (newQuantity <= 0) {
                    removeFromCart(productName);
                } else {
                    item.quantity = newQuantity;
                    updateCartDisplay();
                    updateTotals();
                }
            }
        }

        function updateCartDisplay() {
            const cartContainer = document.getElementById('cart-items');
            
            if (cart.length === 0) {
                cartContainer.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                        </svg>
                        <p>السلة فارغة</p>
                        <p class="text-sm">اختر المنتجات لإضافتها</p>
                    </div>
                `;
            } else {
                cartContainer.innerHTML = cart.map(item => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <h4 class="font-medium text-sm text-gray-900">${item.name}</h4>
                            <p class="text-xs text-gray-500">${item.price.toLocaleString()} ر.س × ${item.quantity}</p>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="updateQuantity('${item.name}', ${item.quantity - 1})" class="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded text-xs">-</button>
                            <span class="w-8 text-center text-sm">${item.quantity}</span>
                            <button onclick="updateQuantity('${item.name}', ${item.quantity + 1})" class="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded text-xs">+</button>
                            <button onclick="removeFromCart('${item.name}')" class="text-red-500 hover:text-red-700 ml-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                `).join('');
            }
        }

        function updateTotals() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountPercent = parseFloat(document.getElementById('discountPercent')?.value || 0);
            const discountAmount = parseFloat(document.getElementById('discountAmount')?.value || 0);
            
            let discount = 0;
            if (discountPercent > 0) {
                discount = subtotal * (discountPercent / 100);
            } else if (discountAmount > 0) {
                discount = discountAmount;
            }
            
            const afterDiscount = subtotal - discount;
            const tax = afterDiscount * 0.15; // 15% VAT
            const total = afterDiscount + tax;

            document.getElementById('subtotal').textContent = subtotal.toLocaleString() + ' ر.س';
            document.getElementById('discount').textContent = discount.toLocaleString() + ' ر.س';
            document.getElementById('tax').textContent = tax.toLocaleString() + ' ر.س';
            document.getElementById('total').textContent = total.toLocaleString() + ' ر.س';

            // Enable/disable checkout button
            const checkoutBtn = document.getElementById('checkout-btn');
            if (checkoutBtn) {
                checkoutBtn.disabled = cart.length === 0;
            }
        }

        function applyDiscount() {
            updateTotals();
            showNotification('تم تطبيق الخصم');
        }

        function clearCart() {
            cart = [];
            updateCartDisplay();
            updateTotals();
            // Clear discount fields
            document.getElementById('discountPercent').value = '';
            document.getElementById('discountAmount').value = '';
            showNotification('تم مسح السلة', 'warning');
        }

        function holdTransaction() {
            if (cart.length === 0) {
                showNotification('السلة فارغة', 'error');
                return;
            }
            showNotification('تم تعليق المعاملة');
        }

        function processCheckout() {
            if (cart.length === 0) {
                showNotification('السلة فارغة', 'error');
                return;
            }
            
            // Check for minimum price validation
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountPercent = parseFloat(document.getElementById('discountPercent')?.value || 0);
            const discountAmount = parseFloat(document.getElementById('discountAmount')?.value || 0);
            
            let discount = 0;
            if (discountPercent > 0) {
                discount = total * (discountPercent / 100);
            } else if (discountAmount > 0) {
                discount = discountAmount;
            }
            
            const finalTotal = total - discount;
            
            // Simulate minimum price check
            const hasLowPriceItems = cart.some(item => {
                const itemTotal = item.price * item.quantity;
                const itemDiscount = discount * (itemTotal / total);
                const finalItemPrice = (itemTotal - itemDiscount) / item.quantity;
                // Assume minimum price is 90% of original price
                return finalItemPrice < (item.price * 0.9);
            });
            
            if (hasLowPriceItems && currentUser.role !== 'مدير النظام') {
                showNotification('يتطلب موافقة المدير للبيع بهذا السعر', 'warning');
                return;
            }
            
            // Simulate successful checkout
            const invoiceNumber = Math.floor(Math.random() * 10000);
            showNotification(`تم إتمام البيع بنجاح! رقم الفاتورة: #${invoiceNumber}`);
            clearCart();
        }

        // Payment method selection
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('payment-method') || e.target.closest('.payment-method')) {
                const button = e.target.classList.contains('payment-method') ? e.target : e.target.closest('.payment-method');
                
                document.querySelectorAll('.payment-method').forEach(btn => {
                    btn.classList.remove('active', 'border-primary-500', 'bg-primary-50', 'text-primary-700');
                    btn.classList.add('border-gray-300', 'text-gray-700');
                });
                
                button.classList.add('active', 'border-primary-500', 'bg-primary-50', 'text-primary-700');
                button.classList.remove('border-gray-300', 'text-gray-700');
            }
        });

        // POS Product filtering
        function filterPOSProducts(category) {
            // Update tab active state
            document.querySelectorAll('.tab-active, .px-4.py-2.rounded-lg.font-medium').forEach(tab => {
                tab.classList.remove('tab-active');
                tab.classList.add('text-gray-600', 'hover:bg-gray-100');
            });
            
            event.target.classList.add('tab-active');
            event.target.classList.remove('text-gray-600', 'hover:bg-gray-100');
            
            showNotification(`تم تصفية المنتجات: ${category === 'all' ? 'جميع المنتجات' : category}`);
        }

        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const globalSearch = document.getElementById('globalSearch');
            if (globalSearch) {
                globalSearch.addEventListener('input', function(e) {
                    const searchTerm = e.target.value.toLowerCase();
                    if (searchTerm.length > 2) {
                        console.log('Searching for:', searchTerm);
                        // Implement global search logic here
                    }
                });
            }
        });

        // Product management functions
        function editProduct(productId) {
            if (window.productsManager) {
                window.productsManager.editProduct(productId);
            }
        }

        function deleteProduct(productId) {
            if (window.productsManager) {
                window.productsManager.deleteProduct(productId);
            }
        }

        function openProductModal(product = null) {
            if (window.productsManager) {
                window.productsManager.openProductModal(product);
            }
        }

        function closeProductModal() {
            if (window.productsManager) {
                window.productsManager.closeProductModal();
            }
        }

        function handleProductSubmit(event) {
            if (window.productsManager) {
                return window.productsManager.saveProduct(event);
            }
            return false;
        }

        function previewImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('imagePreview');
                    const previewImg = document.getElementById('previewImg');
                    previewImg.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        function resetProductFilters() {
            if (window.productsManager) {
                window.productsManager.resetFilters();
            }
        }

        // Inventory management functions
        function viewMovementHistory(productId) {
            showNotification(`عرض تاريخ حركات المنتج #${productId}`, 'info');
            // TODO: Implement movement history modal
        }

        function adjustStock(productId) {
            showNotification(`تعديل مخزون المنتج #${productId}`, 'info');
            // TODO: Implement stock adjustment modal
        }

        function reorderProduct(productId) {
            showNotification(`إعادة طلب المنتج #${productId}`, 'info');
            // TODO: Implement reorder functionality
        }

        // POS functions
        function addToCart(productId, name, price, availableQuantity) {
            if (window.posManager) {
                window.posManager.addToCart(productId, name, price, availableQuantity);
            }
        }

        function removeFromCart(productId) {
            if (window.posManager) {
                window.posManager.removeFromCart(productId);
            }
        }

        function updateQuantity(productId, newQuantity) {
            console.log('🔢 Updating quantity:', productId, newQuantity);
            if (window.posManager) {
                window.posManager.updateQuantity(productId, newQuantity);
            }
        }

        function processPayment(paymentMethod) {
            if (window.posManager) {
                window.posManager.processPayment(paymentMethod);
            }
        }

        function clearCart() {
            if (window.posManager) {
                window.posManager.clearCart();
            }
        }

        function holdTransaction() {
            showNotification('تم تعليق المعاملة', 'info');
            // TODO: Implement hold transaction functionality
        }

        // Modal functions
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            }
        }

        // Stock adjustment functions
        function adjustStock(productId) {
            if (window.inventoryManager) {
                const product = window.inventoryManager.inventory.find(p => p.id == productId);
                if (product) {
                    document.getElementById('adjustProductId').value = productId;
                    document.getElementById('adjustProductName').textContent = product.name;
                    document.getElementById('adjustCurrentStock').textContent = product.currentQuantity || 0;

                    const modal = document.getElementById('stockAdjustmentModal');
                    modal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                }
            }
        }

        async function handleStockAdjustment(event) {
            event.preventDefault();

            const productId = document.getElementById('adjustProductId').value;
            const type = document.getElementById('adjustmentType').value;
            const quantity = parseInt(document.getElementById('adjustmentQuantity').value);
            const reason = document.getElementById('adjustmentReason').value || 'تعديل مخزون';

            try {
                // Get current product info
                let product = null;
                if (window.inventoryManager) {
                    product = window.inventoryManager.inventory.find(p => p.id == productId);
                }

                if (!product) {
                    showNotification('لم يتم العثور على المنتج', 'error');
                    return;
                }

                const currentQuantity = product.currentQuantity || 0;
                let newQuantity = currentQuantity;
                let movementQuantity = 0;

                // Calculate new quantity based on adjustment type
                switch(type) {
                    case 'add':
                        newQuantity = currentQuantity + quantity;
                        movementQuantity = quantity;
                        break;
                    case 'subtract':
                        newQuantity = Math.max(0, currentQuantity - quantity);
                        movementQuantity = -(currentQuantity - newQuantity);
                        break;
                    case 'set':
                        newQuantity = quantity;
                        movementQuantity = quantity - currentQuantity;
                        break;
                }

                // Save movement record
                if (window.salesStorage) {
                    await window.salesStorage.saveInventoryMovement({
                        productId: productId,
                        type: 'adjustment',
                        quantity: movementQuantity,
                        reason: reason,
                        balanceAfter: newQuantity,
                        adjustmentType: type
                    });

                    // Update product quantity
                    await window.salesStorage.updateProductQuantity(productId, newQuantity);
                }

                showNotification(`تم تعديل المخزون للمنتج بنجاح`, 'success');
                closeModal('stockAdjustmentModal');

                // Refresh inventory
                if (window.inventoryManager) {
                    window.inventoryManager.loadInventory();
                }

            } catch (error) {
                console.error('Error adjusting stock:', error);
                showNotification('خطأ في تعديل المخزون', 'error');
            }
        }

        // Movement history functions
        function viewMovementHistory(productId) {
            if (window.inventoryManager) {
                const product = window.inventoryManager.inventory.find(p => p.id == productId);
                if (product) {
                    document.getElementById('historyProductName').textContent = product.name;

                    // Load actual movement history from local storage
                    let movements = [];
                    if (window.salesStorage) {
                        movements = window.salesStorage.getInventoryMovements(productId);
                    }

                    // Sort by date (newest first)
                    movements.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

                    const tbody = document.getElementById('movementHistoryBody');

                    if (movements.length === 0) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                                    لا توجد حركات مخزون لهذا المنتج
                                </td>
                            </tr>
                        `;
                    } else {
                        tbody.innerHTML = movements.map(movement => {
                            const date = new Date(movement.createdAt).toLocaleDateString('ar-SA');
                            const typeText = getMovementTypeText(movement.type);
                            const typeClass = getMovementTypeClass(movement.type);

                            return `
                                <tr>
                                    <td class="px-6 py-4 text-sm text-gray-900">${date}</td>
                                    <td class="px-6 py-4 text-sm">
                                        <span class="px-2 py-1 text-xs rounded-full ${typeClass}">
                                            ${typeText}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">${movement.quantity > 0 ? '+' : ''}${movement.quantity}</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">${movement.balanceAfter}</td>
                                    <td class="px-6 py-4 text-sm text-gray-500">${movement.reason}</td>
                                </tr>
                            `;
                        }).join('');
                    }

                    const modal = document.getElementById('movementHistoryModal');
                    modal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                }
            }
        }

        // Helper functions for movement history
        function getMovementTypeText(type) {
            const types = {
                'sale': 'بيع',
                'purchase': 'شراء',
                'adjustment': 'تعديل',
                'return': 'مرتجع',
                'transfer': 'نقل'
            };
            return types[type] || type;
        }

        function getMovementTypeClass(type) {
            const classes = {
                'sale': 'bg-red-100 text-red-800',
                'purchase': 'bg-green-100 text-green-800',
                'adjustment': 'bg-yellow-100 text-yellow-800',
                'return': 'bg-blue-100 text-blue-800',
                'transfer': 'bg-purple-100 text-purple-800'
            };
            return classes[type] || 'bg-gray-100 text-gray-800';
        }

        // Reorder functions
        function reorderProduct(productId) {
            if (window.inventoryManager) {
                const product = window.inventoryManager.inventory.find(p => p.id == productId);
                if (product) {
                    document.getElementById('reorderProductId').value = productId;
                    document.getElementById('reorderProductName').textContent = product.name;
                    document.getElementById('reorderCurrentStock').textContent = `${product.currentQuantity || 0} قطعة`;

                    // Load suppliers
                    if (window.suppliersManager && window.suppliersManager.suppliers) {
                        const supplierSelect = document.getElementById('reorderSupplier');
                        supplierSelect.innerHTML = '<option value="">اختر المورد</option>' +
                            window.suppliersManager.suppliers.map(supplier =>
                                `<option value="${supplier.id}">${supplier.name}</option>`
                            ).join('');
                    }

                    const modal = document.getElementById('reorderModal');
                    modal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                }
            }
        }

        function handleReorder(event) {
            event.preventDefault();

            const productId = document.getElementById('reorderProductId').value;
            const quantity = document.getElementById('reorderQuantity').value;
            const supplierId = document.getElementById('reorderSupplier').value;
            const notes = document.getElementById('reorderNotes').value;

            // TODO: Send reorder request to backend
            showNotification(`تم إرسال طلب إعادة الطلب للمنتج #${productId}`, 'success');
            closeModal('reorderModal');
        }

        // Confirmation modal
        function showConfirmation(title, message, callback) {
            document.getElementById('confirmationTitle').textContent = title;
            document.getElementById('confirmationMessage').textContent = message;

            const confirmButton = document.getElementById('confirmationButton');
            confirmButton.onclick = () => {
                callback();
                closeModal('confirmationModal');
            };

            const modal = document.getElementById('confirmationModal');
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // Refresh inventory function
        function refreshInventory() {
            if (window.inventoryManager) {
                showNotification('جاري تحديث المخزون...', 'info');
                window.inventoryManager.loadInventory();
            }
        }

        // Suppliers management functions
        function editSupplier(supplierId) {
            if (window.suppliersManager) {
                const supplier = window.suppliersManager.getSupplierById(supplierId);
                window.suppliersManager.openSupplierModal(supplier);
            }
        }

        function deleteSupplier(supplierId) {
            if (window.suppliersManager) {
                window.suppliersManager.deleteSupplier(supplierId);
            }
        }

        function viewSupplierProducts(supplierId) {
            showNotification(`عرض منتجات المورد #${supplierId}`, 'info');
            // TODO: Implement supplier products view
        }

        function openSupplierModal(supplier = null) {
            if (window.suppliersManager) {
                window.suppliersManager.openSupplierModal(supplier);
            }
        }

        function closeSupplierModal() {
            if (window.suppliersManager) {
                window.suppliersManager.closeSupplierModal();
            }
        }

        function handleSupplierSubmit(event) {
            if (window.suppliersManager) {
                return window.suppliersManager.saveSupplier(event);
            }
            return false;
        }

        function viewProductHistory(productId) {
            showNotification(`عرض سجل المنتج #${productId}`);
            // Implement view product history logic
        }

        // Inventory management functions
        function viewMovementHistory(productId) {
            showNotification(`عرض حركات المنتج: ${productId}`);
            // Implement view movement history logic
        }

        function adjustStock(productId) {
            showNotification(`تعديل مخزون المنتج: ${productId}`);
            // Implement adjust stock logic
        }

        function reorderProduct(productId) {
            showNotification(`إعادة طلب المنتج: ${productId}`);
            // Implement reorder product logic
        }

        function startInventoryCount() {
            showNotification('تم بدء عملية الجرد');
            // Implement inventory count logic
        }

        // Supplier management functions
        function editSupplier(supplierId) {
            showNotification(`تحرير المورد #${supplierId}`);
            // Implement edit supplier logic
        }

        function viewSupplierHistory(supplierId) {
            showNotification(`عرض سجل المورد #${supplierId}`);
            // Implement view supplier history logic
        }

        function createPurchaseOrder(supplierId) {
            showNotification(`إنشاء طلب شراء للمورد #${supplierId}`);
            // Implement create purchase order logic
        }

        // Returns management functions
        function viewReturnDetails(returnId) {
            showNotification(`عرض تفاصيل المرتجع #${returnId}`);
            // Implement view return details logic
        }

        function printReturnReceipt(returnId) {
            showNotification(`طباعة إيصال المرتجع #${returnId}`);
            // Implement print return receipt logic
        }

        function approveReturn(returnId) {
            showNotification(`تم الموافقة على المرتجع #${returnId}`);
            // Implement approve return logic
        }

        // Reports functions
        function generateReport(reportType) {
            showNotification(`جاري إنشاء تقرير: ${reportType}`);
            
            const reportDetails = document.getElementById('reportDetails');
            let reportContent = '';
            
            switch(reportType) {
                case 'sales':
                    reportContent = `
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">تقرير المبيعات</h3>
                            <p class="text-sm text-gray-600">تحليل مفصل للمبيعات والأرباح</p>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-green-900">إجمالي المبيعات</h4>
                                    <p class="text-2xl font-bold text-green-600">847,500 ر.س</p>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-blue-900">عدد الفواتير</h4>
                                    <p class="text-2xl font-bold text-blue-600">1,247</p>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-purple-900">متوسط الفاتورة</h4>
                                    <p class="text-2xl font-bold text-purple-600">679 ر.س</p>
                                </div>
                            </div>
                            <div class="chart-container bg-gray-50 rounded-lg flex items-center justify-center">
                                <p class="text-gray-500">رسم بياني للمبيعات</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'inventory':
                    reportContent = `
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">تقرير المخزون</h3>
                            <p class="text-sm text-gray-600">تقييم المخزون الحالي وحالة التوفر</p>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-blue-900">قيمة المخزون</h4>
                                    <p class="text-2xl font-bold text-blue-600">2,847,500 ر.س</p>
                                </div>
                                <div class="bg-orange-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-orange-900">تنبيهات المخزون</h4>
                                    <p class="text-2xl font-bold text-orange-600">23</p>
                                </div>
                                <div class="bg-red-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-red-900">نفد المخزون</h4>
                                    <p class="text-2xl font-bold text-red-600">12</p>
                                </div>
                            </div>
                            <div class="chart-container bg-gray-50 rounded-lg flex items-center justify-center">
                                <p class="text-gray-500">رسم بياني للمخزون</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'profit':
                    reportContent = `
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">تقرير الأرباح والخسائر</h3>
                            <p class="text-sm text-gray-600">تحليل مفصل للأرباح والمصروفات</p>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-green-900">إجمالي الإيرادات</h4>
                                    <p class="text-2xl font-bold text-green-600">847,500 ر.س</p>
                                </div>
                                <div class="bg-red-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-red-900">إجمالي المصروفات</h4>
                                    <p class="text-2xl font-bold text-red-600">557,750 ر.س</p>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-purple-900">صافي الربح</h4>
                                    <p class="text-2xl font-bold text-purple-600">289,750 ر.س</p>
                                </div>
                            </div>
                            <div class="chart-container bg-gray-50 rounded-lg flex items-center justify-center">
                                <p class="text-gray-500">رسم بياني للأرباح والخسائر</p>
                            </div>
                        </div>
                    `;
                    break;
                default:
                    reportContent = `
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">تقرير ${reportType}</h3>
                            <p class="text-sm text-gray-600">تقرير مفصل</p>
                        </div>
                        <div class="p-6">
                            <div class="text-center text-gray-500 py-12">
                                <p class="text-lg font-medium">جاري تحضير التقرير...</p>
                                <p class="text-sm">سيتم عرض البيانات قريباً</p>
                            </div>
                        </div>
                    `;
            }
            
            reportDetails.innerHTML = reportContent;
        }

        function exportReports() {
            showNotification('جاري تصدير التقارير...');
            // Implement export reports logic
        }

        // User management functions - now handled by UsersManager class
        function editUser(userId) {
            if (window.usersManager) {
                window.usersManager.editUser(userId);
            } else {
                showNotification(`تحرير المستخدم #${userId}`);
            }
        }

        function viewUserActivity(userId) {
            if (window.usersManager) {
                window.usersManager.viewUserActivity(userId);
            } else {
                showNotification(`عرض نشاط المستخدم #${userId}`);
            }
        }

        function resetPassword(userId) {
            if (window.usersManager) {
                window.usersManager.resetPassword(userId);
            } else if (confirm('هل أنت متأكد من إعادة تعيين كلمة المرور؟')) {
                showNotification(`تم إعادة تعيين كلمة المرور للمستخدم #${userId}`);
            }
        }

        // Notifications
        function showNotifications() {
            showNotification('عرض جميع التنبيهات');
            // Implement show notifications logic
        }

        // Handle AbortError globally
        window.addEventListener('error', function(event) {
            if (event.error && event.error.name === 'AbortError') {
                // Suppress AbortError from media elements
                event.preventDefault();
                console.log('🔇 AbortError suppressed (media playback interrupted)');
                return false;
            }
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
            if (event.reason && event.reason.name === 'AbortError') {
                // Suppress AbortError from promises
                event.preventDefault();
                console.log('🔇 AbortError promise rejection suppressed');
                return false;
            }
        });

        // Form submissions
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Products Manager
            if (document.getElementById('products-page')) {
                window.productsManager = new ProductsManager();
            }

            // Initialize Inventory Manager
            if (document.getElementById('inventory-page')) {
                window.inventoryManager = new InventoryManager();
            }

            // Initialize POS Manager
            if (document.getElementById('pos-page')) {
                window.posManager = new POSManager();
            }

            // Initialize Suppliers Manager
            if (document.getElementById('suppliers-page')) {
                window.suppliersManager = new SuppliersManager();
            }

            // Initialize Users Manager
            if (document.getElementById('users-page')) {
                console.log('🔧 Initializing Users Manager from DOM...');
                window.usersManager = new UsersManager();
            }

            // Other form submissions
            document.querySelectorAll('form').forEach(form => {
                if (form.id !== 'productForm') {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        showNotification('تم حفظ البيانات بنجاح!');
                        // Close the modal if it exists
                        const modal = this.closest('.modal');
                        if (modal) {
                            modal.classList.remove('active');
                        }
                        this.reset();
                    });
                }
            });
        });

        // Purchase modal functions
        function addPurchaseItem() {
            const purchaseItems = document.getElementById('purchaseItems');
            const newItem = document.createElement('div');
            newItem.className = 'grid grid-cols-1 md:grid-cols-5 gap-3 items-end';
            newItem.innerHTML = `
                <div>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg input-focus" placeholder="البحث أو الباركود">
                </div>
                <div>
                    <input type="number" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-lg input-focus" placeholder="1">
                </div>
                <div>
                    <input type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-lg input-focus" placeholder="0.00">
                </div>
                <div>
                    <input type="text" readonly class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50" placeholder="0.00">
                </div>
                <div>
                    <button type="button" class="w-full px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700" onclick="this.parentElement.parentElement.remove()">حذف</button>
                </div>
            `;
            purchaseItems.appendChild(newItem);
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            updateTotals();
            showNotification('مرحباً بك في ElectroHub Pro 1.0');
            
            console.log('ElectroHub Pro 1.0 - نظام إدارة متجر الإلكترونيات تم تحميله بنجاح');
        });
    </script>

    <!-- Authentication JavaScript -->
    <script src="js/api.js"></script>
    <script src="js/sales-storage.js"></script>
    <script src="js/auth.js"></script>

    <!-- Products Management JavaScript -->
    <script src="js/products-manager.js"></script>

    <!-- Inventory Management JavaScript -->
    <script src="js/inventory-manager.js"></script>

    <!-- POS Management JavaScript -->
    <script src="js/pos-manager.js"></script>

    <!-- Suppliers Management JavaScript -->
    <script src="js/suppliers-manager.js"></script>

    <!-- Users Management JavaScript -->
    <script src="js/users-manager.js"></script>

    <script>
        // Update current user information
        function updateCurrentUserInfo() {
            const userData = getStoredUserData();
            if (userData) {
                const nameElement = document.getElementById('currentUserName');
                const roleElement = document.getElementById('currentUserRole');

                if (nameElement) {
                    nameElement.textContent = userData.fullName || userData.username || 'مستخدم';
                }

                if (roleElement) {
                    roleElement.textContent = userData.roleName || 'مستخدم';
                }

                console.log('👤 Current user:', userData);
            }
        }

        // Initialize authentication and user info when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication first
            if (!checkAuthStatus()) {
                console.log('🔒 User not authenticated, redirecting to login');
                window.location.href = 'login.html';
                return;
            }

            // Update user info
            updateCurrentUserInfo();

            // Setup auto-logout
            setupAutoLogout();
        });
    </script>

    <!-- Stock Adjustment Modal -->
    <div id="stockAdjustmentModal" class="modal">
        <div class="modal-content w-full max-w-md">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">تعديل المخزون</h2>
                <button onclick="closeModal('stockAdjustmentModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="stockAdjustmentForm" onsubmit="handleStockAdjustment(event)">
                <input type="hidden" id="adjustProductId">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">المنتج</label>
                    <p id="adjustProductName" class="text-gray-900 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الكمية الحالية</label>
                    <p id="adjustCurrentStock" class="text-gray-900 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label for="adjustmentType" class="block text-sm font-medium text-gray-700 mb-2">نوع التعديل</label>
                    <select id="adjustmentType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        <option value="add">إضافة للمخزون</option>
                        <option value="subtract">خصم من المخزون</option>
                        <option value="set">تحديد الكمية</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label for="adjustmentQuantity" class="block text-sm font-medium text-gray-700 mb-2">الكمية</label>
                    <input type="number" id="adjustmentQuantity" min="1" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                </div>

                <div class="mb-6">
                    <label for="adjustmentReason" class="block text-sm font-medium text-gray-700 mb-2">السبب</label>
                    <textarea id="adjustmentReason" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                              placeholder="اكتب سبب التعديل..."></textarea>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" onclick="closeModal('stockAdjustmentModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        تطبيق التعديل
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Movement History Modal -->
    <div id="movementHistoryModal" class="modal">
        <div class="modal-content w-full max-w-4xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">تاريخ حركات المخزون</h2>
                <button onclick="closeModal('movementHistoryModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-4">
                <h3 id="historyProductName" class="text-lg font-semibold text-gray-900"></h3>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرصيد بعد</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السبب</th>
                        </tr>
                    </thead>
                    <tbody id="movementHistoryBody" class="bg-white divide-y divide-gray-200">
                        <!-- Movement history will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Reorder Modal -->
    <div id="reorderModal" class="modal">
        <div class="modal-content w-full max-w-md">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">إعادة طلب المنتج</h2>
                <button onclick="closeModal('reorderModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="reorderForm" onsubmit="handleReorder(event)">
                <input type="hidden" id="reorderProductId">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">المنتج</label>
                    <p id="reorderProductName" class="text-gray-900 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">المخزون الحالي</label>
                    <p id="reorderCurrentStock" class="text-red-600 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label for="reorderQuantity" class="block text-sm font-medium text-gray-700 mb-2">كمية الطلب</label>
                    <input type="number" id="reorderQuantity" min="1" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                </div>

                <div class="mb-4">
                    <label for="reorderSupplier" class="block text-sm font-medium text-gray-700 mb-2">المورد</label>
                    <select id="reorderSupplier" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        <option value="">اختر المورد</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label for="reorderNotes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea id="reorderNotes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                              placeholder="ملاحظات إضافية..."></textarea>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" onclick="closeModal('reorderModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg">
                        إرسال الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmationModal" class="modal">
        <div class="modal-content w-full max-w-md">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2" id="confirmationTitle">تأكيد الحذف</h3>
                <p class="text-sm text-gray-500 mb-6" id="confirmationMessage">هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.</p>

                <div class="flex justify-center space-x-3 space-x-reverse">
                    <button type="button" onclick="closeModal('confirmationModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="button" id="confirmationButton" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                        تأكيد الحذف
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>

