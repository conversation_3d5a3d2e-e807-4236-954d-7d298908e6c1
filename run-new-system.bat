@echo off
echo ========================================
echo    نظام إدارة المتجر الإلكتروني المحسن
echo ========================================
echo.

echo 🚀 بدء تشغيل Backend...
cd Backend
start "Backend Server" cmd /k "dotnet restore && dotnet run --project ElectronicsStore.WebAPI"

echo ⏳ انتظار تشغيل الخادم...
timeout /t 8 /nobreak > nul

echo 🌐 فتح صفحة المصادقة المحسنة...
cd ..\Frontend
start "" "auth.html"

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo.
echo 📋 بيانات تجريبية:
echo    اسم المستخدم: Ahmed
echo    كلمة المرور: Ahmed123!
echo.
echo 🔗 الروابط:
echo    Backend API: https://localhost:7001
echo    Swagger UI: https://localhost:7001/swagger
echo    Login Endpoint: https://localhost:7001/api/auth/login12
echo    Frontend: file:///%CD%/auth.html
echo.
echo 🎯 الميزات الجديدة:
echo    ✅ صفحة مصادقة محسنة مع تصميم عصري
echo    ✅ دعم تسجيل الدخول والتسجيل
echo    ✅ مؤشر قوة كلمة المرور
echo    ✅ تصميم متجاوب لجميع الأجهزة
echo    ✅ رسائل خطأ ونجاح واضحة
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
