using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace ElectronicsStore.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private readonly ElectronicsStoreDbContext _context;

    public TestController(ElectronicsStoreDbContext context)
    {
        _context = context;
    }
    [HttpGet]
    public ActionResult<object> Get()
    {
        return Ok(new { 
            message = "Backend يعمل بنجاح!", 
            timestamp = DateTime.Now,
            status = "success"
        });
    }

    [HttpGet("health")]
    public ActionResult<object> Health()
    {
        return Ok(new {
            status = "healthy",
            service = "ElectronicsStore API",
            version = "1.0.0",
            timestamp = DateTime.Now
        });
    }

    [HttpGet("database")]
    public async Task<ActionResult<object>> TestDatabase()
    {
        try
        {
            // Test database connection
            var canConnect = await _context.Database.CanConnectAsync();

            if (!canConnect)
            {
                return Ok(new {
                    status = "error",
                    message = "لا يمكن الاتصال بقاعدة البيانات",
                    canConnect = false,
                    timestamp = DateTime.Now
                });
            }

            // Test if tables exist
            var categoriesCount = await _context.Categories.CountAsync();

            return Ok(new {
                status = "success",
                message = "الاتصال بقاعدة البيانات ناجح",
                canConnect = true,
                categoriesCount = categoriesCount,
                connectionString = _context.Database.GetConnectionString(),
                timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            return Ok(new {
                status = "error",
                message = "خطأ في الاتصال بقاعدة البيانات",
                error = ex.Message,
                innerException = ex.InnerException?.Message,
                timestamp = DateTime.Now
            });
        }
    }

    [HttpGet("categories")]
    public async Task<ActionResult<object>> TestCategories()
    {
        try
        {
            // Test direct access to categories
            var categories = await _context.Categories.Take(5).ToListAsync();

            return Ok(new {
                status = "success",
                message = "تم جلب الأصناف بنجاح",
                count = categories.Count,
                categories = categories.Select(c => new { c.Id, c.Name, c.CreatedAt }),
                timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            return Ok(new {
                status = "error",
                message = "خطأ في جلب الأصناف",
                error = ex.Message,
                innerException = ex.InnerException?.Message,
                timestamp = DateTime.Now
            });
        }
    }
}
